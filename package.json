{"name": "ai-proxy-server", "version": "1.0.0", "description": "专业的 AI 代理服务后端 API 系统，支持 OpenAI、Google Gemini 和 Vertex AI", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["ai", "proxy", "openai", "gemini", "vertex-ai", "api", "server", "sse", "streaming"], "author": "AI Proxy Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "axios": "^1.6.2", "openai": "^4.20.1", "@google/genai": "^0.1.0", "@google-cloud/vertexai": "^1.4.0", "uuid": "^9.0.1", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/uuid": "^9.0.7", "@types/jsonwebtoken": "^9.0.5", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "tsx": "^4.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}