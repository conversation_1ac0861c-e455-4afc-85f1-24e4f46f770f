# 服务器配置
PORT=3000
NODE_ENV=development
HOST=localhost

# OpenAI API 配置（可选，动态模型已预配置）
OPENAI_API_KEY=sk-test-key-for-demo
OPENAI_BASE_URL=https://api.openai.com/v1

# Google Gemini API 配置（可选，动态模型已预配置）
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_BASE_URL=https://generativelanguage.googleapis.com

# 认证配置
JWT_SECRET=demo-jwt-secret-key-for-development
API_KEY_HEADER=X-API-Key

# 速率限制配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# CORS 配置
CORS_ORIGIN=*
CORS_CREDENTIALS=false

# 缓存配置
CACHE_TTL=300
CACHE_MAX_SIZE=1000

# 健康检查配置
HEALTH_CHECK_INTERVAL=30000

# 代理配置
PROXY_TIMEOUT=30000
PROXY_MAX_RETRIES=3
