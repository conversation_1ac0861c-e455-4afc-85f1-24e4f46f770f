# 测试环境配置
NODE_ENV=test
PORT=3001
HOST=localhost

# 测试用的 API Keys (无效的，仅用于测试)
OPENAI_API_KEY=test-openai-key
OPENAI_BASE_URL=https://api.openai.com/v1

GEMINI_API_KEY=test-gemini-key
GEMINI_BASE_URL=https://generativelanguage.googleapis.com

# 认证配置
JWT_SECRET=test-jwt-secret-for-testing
API_KEY_HEADER=X-API-Key

# 速率限制配置 (测试环境更宽松)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# 日志配置
LOG_LEVEL=error
LOG_FILE=logs/test.log

# CORS 配置
CORS_ORIGIN=*
CORS_CREDENTIALS=false

# 缓存配置
CACHE_TTL=60
CACHE_MAX_SIZE=100

# 健康检查配置
HEALTH_CHECK_INTERVAL=60000

# 代理配置
PROXY_TIMEOUT=5000
PROXY_MAX_RETRIES=1
