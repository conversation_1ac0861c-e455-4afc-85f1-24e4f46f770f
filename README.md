# AI Proxy Server

专业的 AI 代理服务后端 API 系统，支持 OpenAI、Google Gemini 和 Vertex AI，提供统一的接口和流式响应功能。

## ✨ 特性

- 🔄 **多 AI 提供商支持**: OpenAI API、Google Gemini API、Google Vertex AI
- 🌊 **双响应模式**: SSE 流式响应和传统 JSON 响应
- 🔐 **多种认证方式**: API Key、Bearer Token、OAuth
- 🚀 **高性能**: 基于 Express.js，支持并发处理
- 📊 **完整监控**: 结构化日志、性能监控、健康检查
- 🛡️ **安全可靠**: 速率限制、CORS、安全头、错误处理
- 🐳 **容器化部署**: Docker 和 Docker Compose 支持
- 📚 **完整文档**: API 文档、使用示例、部署指南

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 8+
- TypeScript 5+

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd ai-proxy-server

# 安装依赖
npm install

# 复制环境变量配置
cp .env.example .env

# 编辑配置文件，填入你的 API 密钥
nano .env
```

### 配置

在 `.env` 文件中配置必要的环境变量：

```env
# 服务器配置
PORT=3000
NODE_ENV=development

# OpenAI API 配置
OPENAI_API_KEY=your_openai_api_key_here

# Google Gemini API 配置
GEMINI_API_KEY=your_gemini_api_key_here

# Google Vertex AI 配置
GOOGLE_CLOUD_PROJECT=your_project_id
GOOGLE_CLOUD_LOCATION=us-central1

# 认证配置
JWT_SECRET=your_jwt_secret_here
```

### 运行

```bash
# 开发模式
npm run dev

# 构建
npm run build

# 生产模式
npm start

# 运行测试
npm test
```

## 📖 API 文档

### 基础信息

- **Base URL**: `http://localhost:3000`
- **API Version**: `v1`
- **Content-Type**: `application/json`

### 认证

支持两种认证方式：

#### 1. API Key 认证
```http
X-API-Key: your_api_key_here
```

#### 2. Bearer Token 认证
```http
Authorization: Bearer your_jwt_token_here
```

### 核心端点

#### 聊天完成 API

**POST** `/v1/chat/completions`

生成聊天完成响应，支持流式和非流式模式。

**请求体**:
```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "Hello, world!"
    }
  ],
  "temperature": 0.7,
  "max_tokens": 100,
  "stream": false,
  "provider": "openai"
}
```

**响应** (非流式):
```json
{
  "success": true,
  "data": {
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
      {
        "index": 0,
        "message": {
          "role": "assistant",
          "content": "Hello! How can I help you today?"
        },
        "finish_reason": "stop"
      }
    ],
    "usage": {
      "prompt_tokens": 9,
      "completion_tokens": 12,
      "total_tokens": 21
    }
  }
}
```

**流式响应** (设置 `stream: true`):
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"role":"assistant","content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"!"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}
```

#### 模型列表 API

**GET** `/v1/models`

获取支持的模型列表。

**查询参数**:
- `provider` (可选): 过滤指定提供商的模型
- `search` (可选): 搜索关键词
- `page` (可选): 页码，默认 1
- `limit` (可选): 每页数量，默认 20

**响应**:
```json
{
  "success": true,
  "data": {
    "object": "list",
    "data": [
      {
        "id": "gpt-3.5-turbo",
        "object": "model",
        "created": **********,
        "owned_by": "openai",
        "provider": "openai"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3
    }
  }
}
```

#### 健康检查 API

**GET** `/health`

基础健康检查。

**GET** `/health/detailed`

详细健康检查，包含所有服务状态。

## 💡 使用示例

### JavaScript/Node.js

```javascript
const axios = require('axios');

// 非流式请求
async function chatCompletion() {
  try {
    const response = await axios.post('http://localhost:3000/v1/chat/completions', {
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'user', content: 'Hello, world!' }
      ],
      temperature: 0.7,
      max_tokens: 100
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'your_api_key_here'
      }
    });
    
    console.log(response.data.data.choices[0].message.content);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// 流式请求
async function streamChatCompletion() {
  try {
    const response = await axios.post('http://localhost:3000/v1/chat/completions', {
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'user', content: 'Tell me a story' }
      ],
      stream: true
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'your_api_key_here',
        'Accept': 'text/event-stream'
      },
      responseType: 'stream'
    });
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            const content = data.choices[0]?.delta?.content;
            if (content) {
              process.stdout.write(content);
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    });
  } catch (error) {
    console.error('Error:', error.message);
  }
}
```

### Python

```python
import requests
import json

# 非流式请求
def chat_completion():
    url = "http://localhost:3000/v1/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "your_api_key_here"
    }
    data = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {"role": "user", "content": "Hello, world!"}
        ],
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 200:
        result = response.json()
        print(result["data"]["choices"][0]["message"]["content"])
    else:
        print(f"Error: {response.status_code} - {response.text}")

# 流式请求
def stream_chat_completion():
    url = "http://localhost:3000/v1/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "your_api_key_here",
        "Accept": "text/event-stream"
    }
    data = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {"role": "user", "content": "Tell me a story"}
        ],
        "stream": True
    }
    
    response = requests.post(url, headers=headers, json=data, stream=True)
    
    for line in response.iter_lines():
        if line:
            line = line.decode('utf-8')
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])
                    content = data.get("choices", [{}])[0].get("delta", {}).get("content")
                    if content:
                        print(content, end="", flush=True)
                except json.JSONDecodeError:
                    pass
```

### cURL

```bash
# 非流式请求
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key_here" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ],
    "temperature": 0.7,
    "max_tokens": 100
  }'

# 流式请求
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key_here" \
  -H "Accept: text/event-stream" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Tell me a story"}
    ],
    "stream": true
  }'
```

## 🐳 Docker 部署

### 使用 Docker Compose (推荐)

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f ai-proxy

# 停止服务
docker-compose down
```

### 使用 Docker

```bash
# 构建镜像
docker build -t ai-proxy-server .

# 运行容器
docker run -d \
  --name ai-proxy \
  -p 3000:3000 \
  -e OPENAI_API_KEY=your_key \
  -e GEMINI_API_KEY=your_key \
  -e JWT_SECRET=your_secret \
  ai-proxy-server
```

## 🔧 配置选项

详细的配置选项请参考 `.env.example` 文件。主要配置项包括：

- **服务器配置**: 端口、主机、环境
- **AI API 配置**: 各提供商的 API 密钥和端点
- **认证配置**: JWT 密钥、API 密钥头
- **速率限制**: 时间窗口、最大请求数
- **日志配置**: 日志级别、文件路径
- **CORS 配置**: 允许的源、凭据

## 📊 监控和日志

### 健康检查端点

- `GET /health` - 基础健康检查
- `GET /health/detailed` - 详细健康检查
- `GET /health/ping` - 简单存活检查
- `GET /health/ready` - 就绪检查
- `GET /health/metrics` - 服务指标

### 日志

日志文件位于 `logs/` 目录：
- `app.log` - 应用日志
- `error.log` - 错误日志
- `exceptions.log` - 未捕获异常
- `rejections.log` - 未处理的 Promise 拒绝

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🆘 支持

如有问题，请提交 Issue 或联系维护者。
