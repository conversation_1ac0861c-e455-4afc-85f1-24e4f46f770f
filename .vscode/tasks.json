{"version": "2.0.0", "tasks": [{"label": "npm: install", "type": "npm", "script": "install", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: build", "type": "npm", "script": "build", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "npm: dev", "type": "npm", "script": "dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "typescript", "source": "ts", "applyTo": "closedDocuments", "fileLocation": ["relative", "${workspaceRoot}"], "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "Local:\\s+http://localhost:\\d+"}}}}, {"label": "npm: start", "type": "npm", "script": "start", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": false}, "dependsOn": "npm: build"}, {"label": "npm: test", "type": "npm", "script": "test", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: test:watch", "type": "npm", "script": "test:watch", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": []}, {"label": "npm: lint", "type": "npm", "script": "lint", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"]}, {"label": "npm: lint:fix", "type": "npm", "script": "lint:fix", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"]}, {"label": "Docker: Build", "type": "shell", "command": "docker", "args": ["build", "-t", "ai-proxy-server", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Docker: Run", "type": "shell", "command": "docker", "args": ["run", "-d", "--name", "ai-proxy", "-p", "3000:3000", "--env-file", ".env", "ai-proxy-server"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "dependsOn": "Docker: Build", "problemMatcher": []}, {"label": "Docker Compose: Up", "type": "shell", "command": "docker-compose", "args": ["up", "-d"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Docker Compose: Down", "type": "shell", "command": "docker-compose", "args": ["down"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "测试动态模型", "type": "shell", "command": "node", "args": ["examples/test-dynamic-models.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "运行 cURL 示例", "type": "shell", "command": "bash", "args": ["examples/curl/api-examples.sh"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}