{"version": "0.2.0", "configurations": [{"name": "启动 AI Proxy Server (开发模式)", "type": "node", "request": "launch", "program": "${workspaceFolder}/src/index.ts", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "runtimeArgs": ["-r", "ts-node/register"], "env": {"NODE_ENV": "development", "PORT": "3000", "LOG_LEVEL": "debug"}, "envFile": "${workspaceFolder}/.env", "console": "integratedTerminal", "restart": true, "protocol": "inspector", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"name": "启动 AI Proxy Server (生产模式)", "type": "node", "request": "launch", "program": "${workspaceFolder}/dist/index.js", "env": {"NODE_ENV": "production", "PORT": "3000"}, "envFile": "${workspaceFolder}/.env", "console": "integratedTerminal", "preLaunchTask": "npm: build", "skipFiles": ["<node_internals>/**"]}, {"name": "运行测试", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "test"}, "envFile": "${workspaceFolder}/.env.test"}, {"name": "运行单个测试文件", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["${relativeFile}", "--runInBand"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "test"}, "envFile": "${workspaceFolder}/.env.test"}, {"name": "调试动态模型测试", "type": "node", "request": "launch", "program": "${workspaceFolder}/examples/test-dynamic-models.js", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}, {"name": "运行 JavaScript 示例", "type": "node", "request": "launch", "program": "${workspaceFolder}/examples/javascript/dynamic-models.js", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}, {"name": "附加到运行中的进程", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "remoteRoot": "/app", "skipFiles": ["<node_internals>/**"]}]}