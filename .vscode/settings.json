{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "files.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/.DS_Store": true, "**/logs": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/logs": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/logs/**": true}, "emmet.includeLanguages": {"typescript": "html"}, "json.schemas": [{"fileMatch": ["package.json"], "url": "https://json.schemastore.org/package.json"}, {"fileMatch": ["tsconfig.json", "tsconfig.*.json"], "url": "https://json.schemastore.org/tsconfig.json"}], "terminal.integrated.env.osx": {"NODE_ENV": "development"}, "terminal.integrated.env.linux": {"NODE_ENV": "development"}, "terminal.integrated.env.windows": {"NODE_ENV": "development"}, "debug.console.fontSize": 14, "debug.console.lineHeight": 20, "workbench.colorCustomizations": {"terminal.background": "#1e1e1e", "terminal.foreground": "#d4d4d4"}, "extensions.recommendations": ["ms-vscode.vscode-typescript-next", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "ms-vscode.vscode-json", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-node-azure-pack"]}