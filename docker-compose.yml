version: '3.8'

services:
  ai-proxy:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: ai-proxy-server
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOST=0.0.0.0
      # AI API 配置（从环境变量或 .env 文件加载）
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}
      - GOOGLE_CLOUD_LOCATION=${GOOGLE_CLOUD_LOCATION:-us-central1}
      # 认证配置
      - JWT_SECRET=${JWT_SECRET}
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - LOG_FILE=/app/logs/app.log
      # 速率限制配置
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-900000}
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-100}
      # CORS 配置
      - CORS_ORIGIN=${CORS_ORIGIN:-*}
      - CORS_CREDENTIALS=${CORS_CREDENTIALS:-false}
    volumes:
      # 持久化日志
      - ./logs:/app/logs
      # Google Cloud 服务账号密钥（如果使用）
      - ${GOOGLE_APPLICATION_CREDENTIALS:-./dummy}:/app/service-account-key.json:ro
    networks:
      - ai-proxy-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health/ping', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # 可选：添加 Redis 用于缓存和会话存储
  redis:
    image: redis:7-alpine
    container_name: ai-proxy-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - ai-proxy-network
    command: redis-server --appendonly yes
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.1'

  # 可选：添加 Nginx 作为反向代理
  nginx:
    image: nginx:alpine
    container_name: ai-proxy-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - ai-proxy-network
    depends_on:
      - ai-proxy
    deploy:
      resources:
        limits:
          memory: 64M
          cpus: '0.1'

volumes:
  redis-data:
    driver: local

networks:
  ai-proxy-network:
    driver: bridge
