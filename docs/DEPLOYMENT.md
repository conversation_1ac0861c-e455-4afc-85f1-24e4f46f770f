# 部署指南

本指南详细介绍了如何在不同环境中部署 AI Proxy Server。

## 🚀 部署选项

### 1. Docker Compose 部署 (推荐)

最简单的部署方式，包含所有必要的服务。

#### 准备工作

1. 确保安装了 Docker 和 Docker Compose
2. 克隆项目到服务器
3. 配置环境变量

```bash
# 克隆项目
git clone <repository-url>
cd ai-proxy-server

# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
nano .env
```

#### 配置环境变量

在 `.env` 文件中设置以下必需的环境变量：

```env
# 基础配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# AI API 密钥
OPENAI_API_KEY=sk-your-openai-key-here
GEMINI_API_KEY=your-gemini-key-here
GOOGLE_CLOUD_PROJECT=your-gcp-project-id
GOOGLE_CLOUD_LOCATION=us-central1

# 安全配置
JWT_SECRET=your-very-secure-jwt-secret-here

# 可选配置
LOG_LEVEL=info
CORS_ORIGIN=*
RATE_LIMIT_MAX_REQUESTS=100
```

#### 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f ai-proxy

# 停止服务
docker-compose down
```

#### 验证部署

```bash
# 健康检查
curl http://localhost:3000/health

# 测试 API
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}]}'
```

### 2. 单独 Docker 部署

如果只需要 AI Proxy Server 服务：

```bash
# 构建镜像
docker build -t ai-proxy-server .

# 运行容器
docker run -d \
  --name ai-proxy \
  -p 3000:3000 \
  -e NODE_ENV=production \
  -e OPENAI_API_KEY=your-key \
  -e GEMINI_API_KEY=your-key \
  -e JWT_SECRET=your-secret \
  -v $(pwd)/logs:/app/logs \
  ai-proxy-server

# 查看日志
docker logs -f ai-proxy
```

### 3. 传统部署 (PM2)

在 Linux 服务器上使用 PM2 进行部署：

#### 安装依赖

```bash
# 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2
npm install -g pm2

# 克隆项目
git clone <repository-url>
cd ai-proxy-server

# 安装依赖
npm ci --only=production

# 构建项目
npm run build
```

#### 配置 PM2

创建 `ecosystem.config.js` 文件：

```javascript
module.exports = {
  apps: [{
    name: 'ai-proxy-server',
    script: 'dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      OPENAI_API_KEY: 'your-openai-key',
      GEMINI_API_KEY: 'your-gemini-key',
      JWT_SECRET: 'your-jwt-secret'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

#### 启动服务

```bash
# 启动服务
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs ai-proxy-server

# 设置开机自启
pm2 startup
pm2 save
```

### 4. Kubernetes 部署

#### 创建配置文件

**ConfigMap** (`k8s/configmap.yaml`):

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-proxy-config
data:
  NODE_ENV: "production"
  PORT: "3000"
  HOST: "0.0.0.0"
  LOG_LEVEL: "info"
  CORS_ORIGIN: "*"
```

**Secret** (`k8s/secret.yaml`):

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: ai-proxy-secrets
type: Opaque
stringData:
  OPENAI_API_KEY: "your-openai-key"
  GEMINI_API_KEY: "your-gemini-key"
  JWT_SECRET: "your-jwt-secret"
```

**Deployment** (`k8s/deployment.yaml`):

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-proxy-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-proxy-server
  template:
    metadata:
      labels:
        app: ai-proxy-server
    spec:
      containers:
      - name: ai-proxy
        image: ai-proxy-server:latest
        ports:
        - containerPort: 3000
        envFrom:
        - configMapRef:
            name: ai-proxy-config
        - secretRef:
            name: ai-proxy-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health/ping
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

**Service** (`k8s/service.yaml`):

```yaml
apiVersion: v1
kind: Service
metadata:
  name: ai-proxy-service
spec:
  selector:
    app: ai-proxy-server
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP
```

**Ingress** (`k8s/ingress.yaml`):

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ai-proxy-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: ai-proxy-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ai-proxy-service
            port:
              number: 80
```

#### 部署到 Kubernetes

```bash
# 应用配置
kubectl apply -f k8s/

# 查看状态
kubectl get pods -l app=ai-proxy-server
kubectl get services
kubectl get ingress

# 查看日志
kubectl logs -l app=ai-proxy-server -f
```

## 🔧 生产环境配置

### 环境变量

生产环境必需的环境变量：

```env
# 基础配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# AI API 配置
OPENAI_API_KEY=sk-your-real-openai-key
GEMINI_API_KEY=your-real-gemini-key
GOOGLE_CLOUD_PROJECT=your-production-project
GOOGLE_CLOUD_LOCATION=us-central1

# 安全配置
JWT_SECRET=your-very-secure-random-string-at-least-32-chars
API_KEY_HEADER=X-API-Key

# 日志配置
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log

# 速率限制
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS 配置
CORS_ORIGIN=https://your-frontend-domain.com
CORS_CREDENTIALS=false

# 性能配置
PROXY_TIMEOUT=30000
PROXY_MAX_RETRIES=3
```

### SSL/TLS 配置

#### 使用 Let's Encrypt

```bash
# 安装 Certbot
sudo apt-get install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### 使用自签名证书 (开发环境)

```bash
# 生成证书
mkdir ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem -out ssl/cert.pem
```

### 反向代理配置

#### Nginx 配置

参考项目中的 `nginx.conf` 文件，主要配置包括：

- SSL/TLS 终止
- 速率限制
- 负载均衡
- 静态文件缓存
- 安全头设置

#### Apache 配置

```apache
<VirtualHost *:443>
    ServerName your-domain.com
    
    SSLEngine on
    SSLCertificateFile /path/to/cert.pem
    SSLCertificateKeyFile /path/to/key.pem
    
    ProxyPreserveHost On
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/
    
    # SSE 支持
    ProxyPass /v1/chat/completions http://localhost:3000/v1/chat/completions nocanon
    ProxyPassReverse /v1/chat/completions http://localhost:3000/v1/chat/completions
    
    Header always set Strict-Transport-Security "max-age=63072000"
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
</VirtualHost>
```

## 📊 监控和日志

### 日志管理

#### 使用 ELK Stack

```yaml
# docker-compose.yml 中添加
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:8.5.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.5.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

#### 使用 Fluentd

```yaml
# fluentd 配置
<source>
  @type tail
  path /app/logs/*.log
  pos_file /var/log/fluentd/ai-proxy.log.pos
  tag ai-proxy.*
  format json
</source>

<match ai-proxy.**>
  @type elasticsearch
  host elasticsearch
  port 9200
  index_name ai-proxy
</match>
```

### 性能监控

#### 使用 Prometheus + Grafana

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ai-proxy'
    static_configs:
      - targets: ['ai-proxy:3000']
    metrics_path: '/health/metrics'
```

#### 健康检查

设置定期健康检查：

```bash
# 创建健康检查脚本
cat > health_check.sh << 'EOF'
#!/bin/bash
HEALTH_URL="http://localhost:3000/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy (HTTP $RESPONSE)"
    exit 1
fi
EOF

chmod +x health_check.sh

# 添加到 crontab
echo "*/5 * * * * /path/to/health_check.sh" | crontab -
```

## 🔒 安全最佳实践

### 1. API 密钥管理

- 使用环境变量存储 API 密钥
- 定期轮换 API 密钥
- 使用密钥管理服务 (AWS KMS, Azure Key Vault)

### 2. 网络安全

- 使用 HTTPS
- 配置防火墙规则
- 限制入站连接
- 使用 VPN 或私有网络

### 3. 访问控制

- 实施强认证机制
- 使用 JWT Token 进行会话管理
- 配置适当的 CORS 策略
- 实施速率限制

### 4. 监控和审计

- 记录所有 API 调用
- 监控异常活动
- 设置告警机制
- 定期安全审计

## 🚨 故障排除

### 常见问题

#### 1. 服务无法启动

```bash
# 检查端口占用
sudo netstat -tlnp | grep :3000

# 检查环境变量
env | grep -E "(OPENAI|GEMINI|JWT)"

# 查看详细日志
docker-compose logs ai-proxy
```

#### 2. API 调用失败

```bash
# 测试网络连接
curl -v http://localhost:3000/health

# 检查 API 密钥
curl -H "X-API-Key: your-key" http://localhost:3000/v1/models

# 查看错误日志
tail -f logs/error.log
```

#### 3. 性能问题

```bash
# 检查资源使用
docker stats ai-proxy

# 查看进程状态
pm2 monit

# 分析日志
grep "duration" logs/app.log | tail -100
```

### 日志分析

```bash
# 查看错误统计
grep "ERROR" logs/app.log | wc -l

# 分析响应时间
grep "duration" logs/app.log | awk '{print $NF}' | sort -n

# 查看最频繁的错误
grep "ERROR" logs/app.log | cut -d'"' -f4 | sort | uniq -c | sort -nr
```

## 📈 扩展和优化

### 水平扩展

1. 使用负载均衡器
2. 部署多个实例
3. 使用 Redis 进行会话共享
4. 实施数据库读写分离

### 性能优化

1. 启用 HTTP/2
2. 使用 CDN
3. 实施缓存策略
4. 优化数据库查询
5. 使用连接池

### 高可用性

1. 多区域部署
2. 自动故障转移
3. 数据备份和恢复
4. 灾难恢复计划

## 📞 支持

如果在部署过程中遇到问题：

1. 查看项目文档
2. 检查 GitHub Issues
3. 提交新的 Issue
4. 联系技术支持

---

**注意**: 在生产环境中部署前，请确保：
- 所有敏感信息都已正确配置
- 已进行充分的测试
- 已设置监控和告警
- 已制定备份和恢复计划
