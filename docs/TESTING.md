# 测试指南

## 概述

本项目包含完整的测试套件，包括单元测试和集成测试。

## 测试命令

### 运行所有测试
```bash
npm test
```

### 运行单元测试（推荐）
```bash
npm run test:unit
```

### 监视模式
```bash
npm run test:watch
```

### 测试覆盖率
```bash
npm run test:coverage
```

## VS Code 调试配置

项目包含以下调试配置：

1. **运行单元测试** - 运行所有单元测试
2. **运行测试** - 运行完整测试套件
3. **运行单个测试文件** - 调试当前打开的测试文件

## 测试结构

```
tests/
├── unit/                 # 单元测试
│   └── adapters/         # 适配器测试
│       └── openai.test.ts
├── integration/          # 集成测试
│   └── api.test.ts
└── setup.ts             # 测试环境设置
```

## 当前测试状态

### ✅ 通过的测试
- **OpenAI 适配器单元测试** (12/12 通过)
  - 构造函数初始化
  - 请求验证
  - 请求转换
  - 响应转换
  - 模型支持检查

### ⚠️ 已知问题
- 集成测试需要有效的 API Keys 才能完全通过
- 某些测试依赖外部服务，可能在网络问题时失败

## 测试最佳实践

1. **运行单元测试优先** - 使用 `npm run test:unit` 进行快速验证
2. **使用调试模式** - 在 VS Code 中使用调试配置来深入了解测试行为
3. **检查覆盖率** - 定期运行覆盖率测试确保代码质量
4. **隔离测试** - 每个测试应该独立运行，不依赖其他测试的状态

## 环境配置

测试使用独立的环境配置：
- **配置文件**: `.env.test`
- **日志级别**: error（减少测试输出噪音）
- **端口**: 3001（避免与开发服务器冲突）

## 添加新测试

1. 在 `tests/unit/` 或 `tests/integration/` 中创建测试文件
2. 使用 `.test.ts` 或 `.spec.ts` 后缀
3. 导入必要的测试工具和被测试的模块
4. 编写描述性的测试用例

示例：
```typescript
import { describe, it, expect } from '@jest/globals';
import { YourModule } from '@/path/to/module';

describe('YourModule', () => {
  it('should do something', () => {
    const result = YourModule.doSomething();
    expect(result).toBe(expectedValue);
  });
});
```
