# 动态模型配置功能

AI Proxy Server 支持动态模型配置功能，允许您通过简单的配置来添加和管理多个 AI 模型，而无需修改代码。

## 🌟 功能特性

- **动态配置**: 无需重启服务即可添加、修改、删除模型配置
- **多提供商支持**: 支持任何 OpenAI 兼容的 API 端点
- **统一接口**: 前端只需发送模型名称，后端自动路由到对应配置
- **灵活管理**: 支持激活/停用模型，便于管理
- **完整功能**: 支持流式和非流式响应

## 📋 配置格式

每个动态模型配置包含以下字段：

```typescript
interface DynamicModelConfig {
  name: string;              // 模型名称（用户友好的名称）
  provider: 'openai' | 'gemini' | 'vertex-ai';  // 提供商类型
  apiKey: string;            // API 密钥
  baseUrl: string;           // API 基础 URL
  modelName: string;         // 实际的模型名称
  supportsVision?: boolean;  // 是否支持视觉功能
  isActive?: boolean;        // 是否激活
  temperature?: number;      // 默认温度参数
  maxTokens?: number;        // 默认最大令牌数
  description?: string;      // 模型描述
}
```

## 🚀 快速开始

### 1. 预配置模型

系统已预配置了以下模型：

```javascript
const defaultModels = [
  {
    name: '夏目官方模型1',
    provider: 'openai',
    apiKey: 'sk-jipjycienusxsfdptoweqvagdillzrumjjtcblfjfsrdhqxk',
    baseUrl: 'https://api.siliconflow.cn',
    modelName: 'Qwen/Qwen2.5-VL-72B-Instruct',
    supportsVision: true,
    isActive: true,
  },
  {
    name: '夏目官方模型2',
    provider: 'openai',
    apiKey: 'sk-YnVBTQs0OLXNk2aQFElOtFPOEqQLe0kaQEbivMvkuFFKqYpn',
    baseUrl: 'https://tbai.xin',
    modelName: 'gemini-2.5-pro',
    supportsVision: true,
    isActive: true,
  },
  {
    name: '需科学上网',
    provider: 'openai',
    apiKey: 'sk-fCr04jsVUMdECSXyfSBYreh6da0ePzTwVmdctxSIQnGgfSxh',
    baseUrl: 'https://x666.me',
    modelName: 'gemini-2.5-pro',
    supportsVision: true,
    isActive: true,
  }
];
```

### 2. 使用动态模型

前端只需要发送模型名称：

```javascript
const response = await fetch('/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-api-key'
  },
  body: JSON.stringify({
    model: '夏目官方模型1',  // 直接使用配置的模型名称
    messages: [
      { role: 'user', content: 'Hello, world!' }
    ]
  })
});
```

## 🔧 API 端点

### 获取动态模型列表

```http
GET /v1/models/dynamic
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "object": "list",
    "data": [
      {
        "id": "夏目官方模型1",
        "name": "夏目官方模型1",
        "provider": "openai",
        "actualModel": "Qwen/Qwen2.5-VL-72B-Instruct",
        "baseUrl": "https://api.siliconflow.cn",
        "supportsVision": true,
        "isActive": true,
        "description": "通义千问视觉大模型，支持图像理解",
        "isDynamic": true
      }
    ],
    "total": 3
  }
}
```

### 添加动态模型

```http
POST /v1/models/dynamic
Content-Type: application/json
X-API-Key: your-api-key

{
  "name": "我的自定义模型",
  "provider": "openai",
  "apiKey": "sk-your-api-key",
  "baseUrl": "https://api.example.com",
  "modelName": "custom-model-v1",
  "supportsVision": false,
  "isActive": true,
  "description": "自定义模型描述"
}
```

### 更新动态模型

```http
PUT /v1/models/dynamic/我的自定义模型
Content-Type: application/json
X-API-Key: your-api-key

{
  "isActive": false,
  "description": "更新后的描述"
}
```

### 删除动态模型

```http
DELETE /v1/models/dynamic/我的自定义模型
X-API-Key: your-api-key
```

### 激活/停用模型

```http
PATCH /v1/models/dynamic/我的自定义模型/toggle
Content-Type: application/json
X-API-Key: your-api-key

{
  "isActive": true
}
```

## 💡 使用示例

### JavaScript 示例

```javascript
// 获取动态模型列表
async function getDynamicModels() {
  const response = await fetch('/v1/models/dynamic');
  const data = await response.json();
  return data.data.data;
}

// 使用动态模型聊天
async function chatWithDynamicModel(modelName, message) {
  const response = await fetch('/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': 'your-api-key'
    },
    body: JSON.stringify({
      model: modelName,
      messages: [{ role: 'user', content: message }]
    })
  });
  
  const data = await response.json();
  return data.data.choices[0].message.content;
}

// 使用示例
async function example() {
  const models = await getDynamicModels();
  console.log('可用模型:', models.map(m => m.name));
  
  const reply = await chatWithDynamicModel('夏目官方模型1', '你好！');
  console.log('回复:', reply);
}
```

### Python 示例

```python
import requests

class DynamicModelClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.headers = {
            'Content-Type': 'application/json',
            'X-API-Key': api_key
        }
    
    def get_dynamic_models(self):
        response = requests.get(f'{self.base_url}/v1/models/dynamic')
        return response.json()['data']['data']
    
    def chat_with_model(self, model_name, message):
        response = requests.post(
            f'{self.base_url}/v1/chat/completions',
            headers=self.headers,
            json={
                'model': model_name,
                'messages': [{'role': 'user', 'content': message}]
            }
        )
        return response.json()['data']['choices'][0]['message']['content']

# 使用示例
client = DynamicModelClient('http://localhost:3000', 'your-api-key')
models = client.get_dynamic_models()
print('可用模型:', [m['name'] for m in models])

reply = client.chat_with_model('夏目官方模型1', '你好！')
print('回复:', reply)
```

## 🧪 测试功能

项目提供了完整的测试脚本：

```bash
# 运行动态模型功能测试
node examples/test-dynamic-models.js

# 运行 JavaScript 示例
node examples/javascript/dynamic-models.js
```

## 🎯 最佳实践

### 1. 模型命名

- 使用有意义的名称，如 "夏目官方模型1"
- 避免使用特殊字符和空格
- 保持名称简洁明了

### 2. API 密钥管理

- 定期轮换 API 密钥
- 使用环境变量存储敏感信息
- 为不同环境使用不同的密钥

### 3. 性能优化

- 合理设置 `temperature` 和 `maxTokens` 默认值
- 根据需要激活/停用模型
- 监控模型使用情况和成本

### 4. 错误处理

- 实现重试机制
- 监控模型可用性
- 提供备用模型选项

## 🔒 安全考虑

1. **API 密钥保护**: 确保 API 密钥安全存储，不要在客户端暴露
2. **访问控制**: 使用认证机制保护模型管理端点
3. **输入验证**: 严格验证所有输入参数
4. **速率限制**: 实施适当的速率限制防止滥用

## 🚨 故障排除

### 常见问题

1. **模型不可用**
   - 检查 API 密钥是否正确
   - 验证 baseUrl 是否可访问
   - 确认模型名称是否正确

2. **请求失败**
   - 检查网络连接
   - 验证请求格式
   - 查看服务器日志

3. **性能问题**
   - 检查模型响应时间
   - 优化请求参数
   - 考虑使用缓存

### 调试技巧

```bash
# 查看服务器日志
docker-compose logs -f ai-proxy

# 测试模型连接
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{"model":"夏目官方模型1","messages":[{"role":"user","content":"test"}]}'

# 检查模型状态
curl http://localhost:3000/v1/models/dynamic
```

## 📚 相关文档

- [API 文档](API.md)
- [部署指南](DEPLOYMENT.md)
- [主要 README](../README.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进动态模型功能！
