# AI Proxy Server API 文档

## 概述

AI Proxy Server 提供统一的 RESTful API 接口，支持多个 AI 服务提供商，包括 OpenAI、Google Gemini 和 Vertex AI。

## 基础信息

- **Base URL**: `https://your-domain.com` 或 `http://localhost:3000`
- **API Version**: `v1`
- **Content-Type**: `application/json`
- **支持的方法**: GET, POST, OPTIONS

## 认证

### API Key 认证

在请求头中包含 API Key：

```http
X-API-Key: your_api_key_here
```

### Bearer Token 认证

在请求头中包含 JWT Token：

```http
Authorization: Bearer your_jwt_token_here
```

### Gemini Bearer 认证模式

对于 Gemini API，支持特殊的 Bearer 认证模式（类似 vertex2openai 项目）：

```http
Authorization: Bearer your_gemini_api_key
```

## 错误处理

所有错误响应都遵循统一格式：

```json
{
  "success": false,
  "error": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": "2023-12-07T10:30:00.000Z",
  "requestId": "req_123456789"
}
```

### 常见错误码

| 状态码 | 错误码 | 描述 |
|--------|--------|------|
| 400 | VALIDATION_ERROR | 请求参数验证失败 |
| 401 | UNAUTHORIZED | 未授权访问 |
| 403 | FORBIDDEN | 访问被拒绝 |
| 404 | NOT_FOUND | 资源不存在 |
| 429 | RATE_LIMITED | 请求频率过高 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

## 端点详情

### 1. 聊天完成

#### POST /v1/chat/completions

生成聊天完成响应，支持流式和非流式模式。

**请求参数**:

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| model | string | 是 | 模型名称 |
| messages | array | 是 | 消息数组 |
| temperature | number | 否 | 温度参数 (0-2) |
| max_tokens | number | 否 | 最大令牌数 |
| top_p | number | 否 | Top-p 参数 (0-1) |
| frequency_penalty | number | 否 | 频率惩罚 (-2 到 2) |
| presence_penalty | number | 否 | 存在惩罚 (-2 到 2) |
| stop | string/array | 否 | 停止序列 |
| stream | boolean | 否 | 是否流式响应 |
| provider | string | 否 | 指定提供商 |

**消息格式**:

```json
{
  "role": "user|assistant|system",
  "content": "消息内容",
  "name": "可选的名称"
}
```

**非流式响应**:

```json
{
  "success": true,
  "data": {
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
      {
        "index": 0,
        "message": {
          "role": "assistant",
          "content": "响应内容"
        },
        "finish_reason": "stop"
      }
    ],
    "usage": {
      "prompt_tokens": 9,
      "completion_tokens": 12,
      "total_tokens": 21
    }
  }
}
```

**流式响应**:

设置 `stream: true` 并在请求头中包含 `Accept: text/event-stream`。

响应格式为 Server-Sent Events：

```
event: start
data: {"type":"start","requestId":"req_123","model":"gpt-3.5-turbo","provider":"openai"}

event: chunk
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"role":"assistant","content":"Hello"},"finish_reason":null}]}

event: chunk
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":" world!"},"finish_reason":null}]}

event: complete
data: {"type":"complete","requestId":"req_123","chunkCount":5,"duration":"1234ms"}
```

#### POST /v1/chat/batch

批量处理聊天完成请求。

**请求参数**:

```json
{
  "requests": [
    {
      "model": "gpt-3.5-turbo",
      "messages": [{"role": "user", "content": "Hello"}]
    },
    {
      "model": "gemini-pro",
      "messages": [{"role": "user", "content": "Hi"}]
    }
  ]
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "responses": [
      {
        "id": "chatcmpl-123",
        "choices": [...]
      },
      {
        "id": "chatcmpl-456",
        "choices": [...]
      }
    ],
    "count": 2
  }
}
```

### 2. 模型管理

#### GET /v1/models

获取支持的模型列表。

**查询参数**:

| 参数 | 类型 | 描述 |
|------|------|------|
| provider | string | 过滤指定提供商 |
| search | string | 搜索关键词 |
| page | number | 页码 (默认 1) |
| limit | number | 每页数量 (默认 20) |

**响应**:

```json
{
  "success": true,
  "data": {
    "object": "list",
    "data": [
      {
        "id": "gpt-3.5-turbo",
        "object": "model",
        "created": **********,
        "owned_by": "openai",
        "provider": "openai",
        "name": "GPT-3.5 Turbo",
        "description": "OpenAI 模型: GPT-3.5 Turbo"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### GET /v1/models/:id

获取指定模型的详细信息。

**响应**:

```json
{
  "success": true,
  "data": {
    "id": "gpt-3.5-turbo",
    "object": "model",
    "created": **********,
    "owned_by": "openai",
    "provider": "openai",
    "name": "GPT-3.5 Turbo",
    "description": "OpenAI 模型: GPT-3.5 Turbo",
    "supported": true,
    "capabilities": {
      "completion": true,
      "streaming": true,
      "chat": true
    }
  }
}
```

#### GET /v1/models/provider/:provider

获取指定提供商的模型列表。

**路径参数**:
- `provider`: 提供商名称 (openai, gemini, vertex-ai)

#### POST /v1/models/validate

验证模型是否支持。

**请求参数**:

```json
{
  "model": "gpt-3.5-turbo",
  "provider": "openai"
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "model": "gpt-3.5-turbo",
    "provider": "openai",
    "supported": true,
    "timestamp": "2023-12-07T10:30:00.000Z"
  }
}
```

### 3. 健康检查

#### GET /health

基础健康检查。

**响应**:

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2023-12-07T10:30:00.000Z",
    "uptime": 123456,
    "version": "1.0.0",
    "services": {
      "api": {
        "status": "up"
      }
    }
  }
}
```

#### GET /health/detailed

详细健康检查。

**查询参数**:
- `provider`: 指定提供商 (可选)

**响应**:

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2023-12-07T10:30:00.000Z",
    "uptime": 123456,
    "version": "1.0.0",
    "services": {
      "api": {
        "status": "up",
        "latency": 0
      },
      "sse": {
        "activeConnections": 5,
        "status": "up"
      },
      "openai": {
        "status": "up",
        "latency": 150
      },
      "gemini": {
        "status": "up",
        "latency": 200
      },
      "vertex-ai": {
        "status": "down",
        "error": "Authentication failed"
      }
    },
    "system": {
      "nodeVersion": "v18.17.0",
      "platform": "linux",
      "memory": {
        "rss": "45.23 MB",
        "heapTotal": "30.15 MB",
        "heapUsed": "25.67 MB"
      }
    }
  }
}
```

#### GET /health/ping

简单存活检查。

**响应**:

```json
{
  "status": "ok",
  "timestamp": "2023-12-07T10:30:00.000Z",
  "uptime": 123456
}
```

#### GET /health/ready

就绪检查。

**响应**:

```json
{
  "status": "ready",
  "timestamp": "2023-12-07T10:30:00.000Z",
  "services": {
    "openai": {"status": "up"},
    "gemini": {"status": "up"},
    "vertex-ai": {"status": "up"}
  }
}
```

#### GET /health/metrics

服务指标。

**响应**:

```json
{
  "success": true,
  "data": {
    "uptime": {
      "seconds": 3600,
      "human": "60 minutes"
    },
    "memory": {
      "rss": 47513600,
      "heapTotal": 31653888,
      "heapUsed": 26934272,
      "heapUsedPercentage": "85.10"
    },
    "sse": {
      "activeConnections": 5
    },
    "process": {
      "pid": 1234,
      "version": "v18.17.0",
      "platform": "linux"
    }
  }
}
```

## 速率限制

API 实施了多层速率限制：

| 端点类型 | 时间窗口 | 最大请求数 |
|----------|----------|------------|
| 健康检查 | 1 分钟 | 1000 |
| 模型列表 | 15 分钟 | 100 |
| AI API | 1 分钟 | 60 |
| 其他端点 | 15 分钟 | 100 |

当达到速率限制时，会返回 429 状态码和相应的错误信息。

## WebSocket 和 SSE

### Server-Sent Events

对于流式响应，使用 Server-Sent Events (SSE)：

1. 设置请求头 `Accept: text/event-stream`
2. 在请求体中设置 `stream: true`
3. 监听以下事件类型：
   - `start`: 流开始
   - `chunk`: 数据块
   - `complete`: 流结束
   - `error`: 错误
   - `ping`: 心跳

### 测试 SSE 连接

使用测试端点验证 SSE 功能：

```bash
curl -N -H "Accept: text/event-stream" \
  http://localhost:3000/v1/chat/test-stream
```

## 示例代码

详细的示例代码请参考 `examples/` 目录，包含：

- JavaScript/Node.js 示例
- Python 示例
- cURL 示例
- 前端 JavaScript 示例

## 版本控制

API 使用语义化版本控制。当前版本为 v1，所有端点都以 `/v1` 为前缀。

## 支持

如有问题或建议，请提交 Issue 或联系技术支持。
