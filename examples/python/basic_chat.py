#!/usr/bin/env python3
"""
AI Proxy Server Python 示例
基础聊天完成功能演示
"""

import requests
import json
import time
from typing import List, Dict, Any, Optional

# 配置
API_BASE_URL = "http://localhost:3000"
API_KEY = "your-api-key-here"  # 替换为你的 API 密钥

class AIProxyClient:
    """AI Proxy Server 客户端"""
    
    def __init__(self, base_url: str = API_BASE_URL, api_key: str = API_KEY):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'X-API-Key': api_key
        })
    
    def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        provider: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """发送聊天完成请求"""
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            **kwargs
        }
        
        if max_tokens:
            payload["max_tokens"] = max_tokens
        if provider:
            payload["provider"] = provider
        
        response = self.session.post(
            f"{self.base_url}/v1/chat/completions",
            json=payload
        )
        response.raise_for_status()
        return response.json()
    
    def get_models(self, provider: Optional[str] = None) -> Dict[str, Any]:
        """获取模型列表"""
        params = {}
        if provider:
            params["provider"] = provider
        
        response = self.session.get(
            f"{self.base_url}/v1/models",
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    def health_check(self, detailed: bool = False) -> Dict[str, Any]:
        """健康检查"""
        endpoint = "/health/detailed" if detailed else "/health"
        response = self.session.get(f"{self.base_url}{endpoint}")
        response.raise_for_status()
        return response.json()
    
    def batch_completions(self, requests_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量聊天完成"""
        payload = {"requests": requests_list}
        response = self.session.post(
            f"{self.base_url}/v1/chat/batch",
            json=payload
        )
        response.raise_for_status()
        return response.json()


def basic_chat_example():
    """基础聊天示例"""
    print("🤖 基础聊天示例")
    print("-" * 50)
    
    client = AIProxyClient()
    
    try:
        response = client.chat_completion(
            messages=[
                {"role": "system", "content": "你是一个有用的助手。"},
                {"role": "user", "content": "请介绍一下 Python 编程语言的特点。"}
            ],
            model="gpt-3.5-turbo",
            temperature=0.7,
            max_tokens=500
        )
        
        result = response["data"]
        print(f"✅ 模型: {result['model']}")
        print(f"📝 回复: {result['choices'][0]['message']['content']}")
        print(f"📊 令牌使用: {result['usage']}")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    except Exception as e:
        print(f"❌ 错误: {e}")


def multi_turn_conversation():
    """多轮对话示例"""
    print("\n🔄 多轮对话示例")
    print("-" * 50)
    
    client = AIProxyClient()
    conversation = [
        {"role": "system", "content": "你是一个编程导师。"}
    ]
    
    questions = [
        "什么是面向对象编程？",
        "能给我一个 Python 类的例子吗？",
        "如何理解继承和多态？"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n👤 问题 {i}: {question}")
        conversation.append({"role": "user", "content": question})
        
        try:
            response = client.chat_completion(
                messages=conversation,
                model="gpt-3.5-turbo",
                temperature=0.7,
                max_tokens=300
            )
            
            assistant_reply = response["data"]["choices"][0]["message"]
            conversation.append(assistant_reply)
            
            print(f"🤖 回答: {assistant_reply['content']}")
            
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            break


def compare_providers():
    """比较不同提供商"""
    print("\n🔍 比较不同提供商")
    print("-" * 50)
    
    client = AIProxyClient()
    prompt = "用一句话解释什么是人工智能。"
    
    providers = [
        {"name": "OpenAI", "model": "gpt-3.5-turbo", "provider": "openai"},
        {"name": "Gemini", "model": "gemini-pro", "provider": "gemini"}
    ]
    
    print(f"📝 问题: {prompt}\n")
    
    for config in providers:
        try:
            start_time = time.time()
            response = client.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                model=config["model"],
                provider=config["provider"],
                temperature=0.7,
                max_tokens=100
            )
            end_time = time.time()
            
            result = response["data"]
            print(f"🤖 {config['name']} ({config['model']}):")
            print(f"   回复: {result['choices'][0]['message']['content']}")
            print(f"   令牌: {result['usage']['total_tokens']}")
            print(f"   耗时: {end_time - start_time:.2f}秒\n")
            
        except Exception as e:
            print(f"❌ {config['name']} 请求失败: {e}\n")


def batch_requests_example():
    """批量请求示例"""
    print("\n📦 批量请求示例")
    print("-" * 50)
    
    client = AIProxyClient()
    
    requests_list = [
        {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": "什么是机器学习？"}],
            "max_tokens": 100
        },
        {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": "什么是深度学习？"}],
            "max_tokens": 100
        },
        {
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": "什么是神经网络？"}],
            "max_tokens": 100
        }
    ]
    
    try:
        response = client.batch_completions(requests_list)
        results = response["data"]["responses"]
        
        print(f"✅ 批量请求完成，共 {len(results)} 个响应:")
        
        for i, result in enumerate(results, 1):
            content = result["choices"][0]["message"]["content"]
            print(f"\n{i}. {content}")
            
    except Exception as e:
        print(f"❌ 批量请求失败: {e}")


def error_handling_example():
    """错误处理示例"""
    print("\n🚨 错误处理示例")
    print("-" * 50)
    
    # 测试无效 API 密钥
    print("测试无效 API 密钥...")
    invalid_client = AIProxyClient(api_key="invalid-key")
    
    try:
        invalid_client.chat_completion(
            messages=[{"role": "user", "content": "Hello"}],
            model="gpt-3.5-turbo"
        )
    except requests.exceptions.HTTPError as e:
        print(f"❌ HTTP 错误: {e.response.status_code}")
        try:
            error_data = e.response.json()
            print(f"   错误信息: {error_data.get('error', 'Unknown error')}")
        except:
            print(f"   响应内容: {e.response.text}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")
    
    # 测试无效请求参数
    print("\n测试无效请求参数...")
    client = AIProxyClient()
    
    try:
        client.chat_completion(
            messages=[],  # 空消息数组
            model=""      # 空模型名
        )
    except requests.exceptions.HTTPError as e:
        print(f"❌ 验证错误: {e.response.status_code}")
        try:
            error_data = e.response.json()
            print(f"   错误信息: {error_data.get('error', 'Unknown error')}")
        except:
            print(f"   响应内容: {e.response.text}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")


def get_models_example():
    """获取模型列表示例"""
    print("\n📋 获取模型列表")
    print("-" * 50)
    
    client = AIProxyClient()
    
    try:
        response = client.get_models()
        models = response["data"]["data"]
        
        print(f"✅ 共找到 {len(models)} 个模型:")
        
        # 按提供商分组显示
        providers = {}
        for model in models:
            provider = model["provider"]
            if provider not in providers:
                providers[provider] = []
            providers[provider].append(model["id"])
        
        for provider, model_list in providers.items():
            print(f"\n🔧 {provider.upper()}:")
            for model_id in model_list[:5]:  # 只显示前5个
                print(f"   - {model_id}")
            if len(model_list) > 5:
                print(f"   ... 还有 {len(model_list) - 5} 个模型")
                
    except Exception as e:
        print(f"❌ 获取模型列表失败: {e}")


def health_check_example():
    """健康检查示例"""
    print("\n🏥 健康检查")
    print("-" * 50)
    
    client = AIProxyClient()
    
    try:
        # 基础健康检查
        response = client.health_check()
        health = response["data"]
        
        print(f"✅ 服务状态: {health['status']}")
        print(f"⏱️  运行时间: {health['uptime'] // 1000} 秒")
        
        # 详细健康检查
        detailed_response = client.health_check(detailed=True)
        detailed_health = detailed_response["data"]
        
        print(f"\n📊 详细状态:")
        for service, status in detailed_health["services"].items():
            emoji = "✅" if status["status"] == "up" else "❌"
            latency = f" ({status.get('latency', 0)}ms)" if status.get('latency') else ""
            print(f"   {emoji} {service}: {status['status']}{latency}")
            
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")


def interactive_chat():
    """交互式聊天"""
    print("\n💬 交互式聊天")
    print("-" * 50)
    print("输入 'quit' 退出聊天\n")
    
    client = AIProxyClient()
    conversation = [
        {"role": "system", "content": "你是一个友好的助手，请用简洁明了的语言回答问题。"}
    ]
    
    while True:
        try:
            user_input = input("👤 你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            conversation.append({"role": "user", "content": user_input})
            
            print("🤖 助手: ", end="", flush=True)
            
            response = client.chat_completion(
                messages=conversation,
                model="gpt-3.5-turbo",
                temperature=0.8,
                max_tokens=300
            )
            
            assistant_reply = response["data"]["choices"][0]["message"]
            conversation.append(assistant_reply)
            
            print(assistant_reply["content"])
            print()
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 请求失败: {e}")


def main():
    """主函数"""
    print("🚀 AI Proxy Server Python 示例\n")
    
    # 运行所有示例
    health_check_example()
    get_models_example()
    basic_chat_example()
    multi_turn_conversation()
    compare_providers()
    batch_requests_example()
    error_handling_example()
    
    # 交互式聊天（可选）
    # interactive_chat()
    
    print("\n✨ 所有示例运行完成！")


if __name__ == "__main__":
    main()
