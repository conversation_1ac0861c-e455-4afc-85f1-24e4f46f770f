#!/bin/bash

# AI Proxy Server cURL 示例脚本
# 演示如何使用 cURL 调用各种 API 端点

# 配置
API_BASE_URL="http://localhost:3000"
API_KEY="your-api-key-here"  # 替换为你的 API 密钥

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印分隔线
print_separator() {
    echo -e "${BLUE}$(printf '=%.0s' {1..60})${NC}"
}

# 打印标题
print_title() {
    echo -e "\n${YELLOW}$1${NC}"
    print_separator
}

# 打印成功消息
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# 打印错误消息
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查 jq 是否安装
check_jq() {
    if ! command -v jq &> /dev/null; then
        print_error "jq 未安装，请先安装 jq 以获得更好的 JSON 格式化输出"
        echo "macOS: brew install jq"
        echo "Ubuntu: sudo apt-get install jq"
        echo "CentOS: sudo yum install jq"
        exit 1
    fi
}

# 健康检查
health_check() {
    print_title "🏥 健康检查"
    
    echo "基础健康检查:"
    curl -s -X GET "$API_BASE_URL/health" | jq '.'
    
    echo -e "\n详细健康检查:"
    curl -s -X GET "$API_BASE_URL/health/detailed" | jq '.'
    
    echo -e "\nPing 检查:"
    curl -s -X GET "$API_BASE_URL/health/ping" | jq '.'
}

# 获取模型列表
get_models() {
    print_title "📋 获取模型列表"
    
    echo "所有模型:"
    curl -s -X GET "$API_BASE_URL/v1/models" \
        -H "X-API-Key: $API_KEY" | jq '.data.data[] | {id: .id, provider: .provider}'
    
    echo -e "\nOpenAI 模型:"
    curl -s -X GET "$API_BASE_URL/v1/models?provider=openai" \
        -H "X-API-Key: $API_KEY" | jq '.data.data[] | .id'
    
    echo -e "\n分页查询 (前5个):"
    curl -s -X GET "$API_BASE_URL/v1/models?limit=5" \
        -H "X-API-Key: $API_KEY" | jq '.data.pagination'
}

# 基础聊天完成
basic_chat() {
    print_title "🤖 基础聊天完成"
    
    echo "发送聊天请求..."
    curl -s -X POST "$API_BASE_URL/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d '{
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "system", "content": "你是一个有用的助手。"},
                {"role": "user", "content": "请用一句话介绍什么是人工智能。"}
            ],
            "temperature": 0.7,
            "max_tokens": 100
        }' | jq '{
            model: .data.model,
            content: .data.choices[0].message.content,
            usage: .data.usage
        }'
}

# 指定提供商的聊天
provider_chat() {
    print_title "🔧 指定提供商聊天"
    
    echo "使用 OpenAI:"
    curl -s -X POST "$API_BASE_URL/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d '{
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": "什么是机器学习？"}],
            "provider": "openai",
            "max_tokens": 50
        }' | jq '.data.choices[0].message.content'
    
    echo -e "\n使用 Gemini:"
    curl -s -X POST "$API_BASE_URL/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d '{
            "model": "gemini-pro",
            "messages": [{"role": "user", "content": "什么是机器学习？"}],
            "provider": "gemini",
            "max_tokens": 50
        }' | jq '.data.choices[0].message.content'
}

# 流式聊天完成
streaming_chat() {
    print_title "🌊 流式聊天完成"
    
    echo "发送流式请求 (前10行):"
    curl -s -X POST "$API_BASE_URL/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -H "Accept: text/event-stream" \
        -d '{
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": "请写一个关于春天的短诗。"}],
            "stream": true,
            "temperature": 0.8,
            "max_tokens": 200
        }' | head -20
}

# 批量请求
batch_requests() {
    print_title "📦 批量请求"
    
    echo "发送批量聊天请求:"
    curl -s -X POST "$API_BASE_URL/v1/chat/batch" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d '{
            "requests": [
                {
                    "model": "gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": "什么是 AI？"}],
                    "max_tokens": 30
                },
                {
                    "model": "gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": "什么是 ML？"}],
                    "max_tokens": 30
                },
                {
                    "model": "gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": "什么是 DL？"}],
                    "max_tokens": 30
                }
            ]
        }' | jq '.data.responses[] | .choices[0].message.content'
}

# 模型验证
validate_model() {
    print_title "✅ 模型验证"
    
    echo "验证 GPT-3.5 模型:"
    curl -s -X POST "$API_BASE_URL/v1/models/validate" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d '{
            "model": "gpt-3.5-turbo",
            "provider": "openai"
        }' | jq '{model: .data.model, supported: .data.supported}'
    
    echo -e "\n验证不存在的模型:"
    curl -s -X POST "$API_BASE_URL/v1/models/validate" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d '{
            "model": "non-existent-model"
        }' | jq '{model: .data.model, supported: .data.supported}'
}

# 错误处理示例
error_handling() {
    print_title "🚨 错误处理示例"
    
    echo "测试无效 API 密钥:"
    curl -s -X POST "$API_BASE_URL/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: invalid-key" \
        -d '{
            "model": "gpt-3.5-turbo",
            "messages": [{"role": "user", "content": "Hello"}]
        }' | jq '{success: .success, error: .error, code: .code}'
    
    echo -e "\n测试无效请求参数:"
    curl -s -X POST "$API_BASE_URL/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d '{
            "model": "",
            "messages": []
        }' | jq '{success: .success, error: .error, code: .code}'
    
    echo -e "\n测试不存在的端点:"
    curl -s -X GET "$API_BASE_URL/non-existent-endpoint" | jq '{success: .success, error: .error, code: .code}'
}

# Bearer Token 认证示例
bearer_auth() {
    print_title "🔐 Bearer Token 认证"
    
    echo "注意: 这需要有效的 JWT Token"
    echo "使用 Bearer Token 进行认证:"
    
    # 这里需要一个有效的 JWT Token
    JWT_TOKEN="your-jwt-token-here"
    
    curl -s -X GET "$API_BASE_URL/v1/models" \
        -H "Authorization: Bearer $JWT_TOKEN" | jq '.success // "认证失败"'
}

# 测试 SSE 连接
test_sse() {
    print_title "🧪 测试 SSE 连接"
    
    echo "测试 SSE 连接 (前10个事件):"
    curl -s -N -H "Accept: text/event-stream" \
        "$API_BASE_URL/v1/chat/test-stream" | head -20
}

# 获取服务指标
get_metrics() {
    print_title "📊 服务指标"
    
    echo "获取服务指标:"
    curl -s -X GET "$API_BASE_URL/health/metrics" | jq '{
        uptime: .data.uptime,
        memory: .data.memory,
        sse: .data.sse,
        process: .data.process
    }'
}

# 性能测试
performance_test() {
    print_title "⚡ 性能测试"
    
    echo "发送10个并发请求..."
    
    for i in {1..10}; do
        (
            start_time=$(date +%s%N)
            response=$(curl -s -X POST "$API_BASE_URL/v1/chat/completions" \
                -H "Content-Type: application/json" \
                -H "X-API-Key: $API_KEY" \
                -d '{
                    "model": "gpt-3.5-turbo",
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 10
                }')
            end_time=$(date +%s%N)
            duration=$(( (end_time - start_time) / 1000000 ))
            
            success=$(echo "$response" | jq -r '.success // false')
            echo "请求 $i: ${duration}ms - $success"
        ) &
    done
    
    wait
    echo "所有并发请求完成"
}

# 主函数
main() {
    echo -e "${BLUE}🚀 AI Proxy Server cURL 示例${NC}\n"
    
    # 检查依赖
    check_jq
    
    # 检查服务是否可用
    if ! curl -s "$API_BASE_URL/health" > /dev/null; then
        print_error "无法连接到 AI Proxy Server ($API_BASE_URL)"
        echo "请确保服务正在运行"
        exit 1
    fi
    
    print_success "连接到 AI Proxy Server 成功"
    
    # 运行所有示例
    health_check
    get_models
    basic_chat
    provider_chat
    streaming_chat
    batch_requests
    validate_model
    error_handling
    test_sse
    get_metrics
    
    # 可选的性能测试
    read -p "是否运行性能测试? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        performance_test
    fi
    
    print_title "✨ 所有示例运行完成"
}

# 如果直接运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
