<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Proxy Server - 聊天界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .provider-selector {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 10px;
        }

        .provider-btn {
            padding: 5px 15px;
            border: 1px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: white;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .provider-btn.active {
            background: rgba(255,255,255,0.3);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.user {
            align-self: flex-end;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.assistant {
            align-self: flex-start;
            background: #f1f3f4;
            color: #333;
            border: 1px solid #e0e0e0;
        }

        .message.system {
            align-self: center;
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            font-size: 14px;
        }

        .message.error {
            align-self: center;
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .typing-indicator {
            align-self: flex-start;
            background: #f1f3f4;
            border: 1px solid #e0e0e0;
            padding: 12px 16px;
            border-radius: 18px;
            display: none;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #999;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: #f9f9f9;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        #messageInput {
            width: 100%;
            min-height: 50px;
            max-height: 120px;
            padding: 12px 50px 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: border-color 0.3s;
        }

        #messageInput:focus {
            border-color: #667eea;
        }

        .send-btn {
            position: absolute;
            right: 8px;
            bottom: 8px;
            width: 35px;
            height: 35px;
            border: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
        }

        .send-btn:hover {
            transform: scale(1.1);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            color: #666;
        }

        .control-group input, .control-group select {
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
        }

        .stream-toggle {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stream-toggle input[type="checkbox"] {
            margin: 0;
        }

        .status-bar {
            padding: 10px 20px;
            background: #f0f0f0;
            border-top: 1px solid #e0e0e0;
            font-size: 12px;
            color: #666;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }

        .status-dot.disconnected {
            background: #dc3545;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 90vh;
            }
            
            .controls {
                flex-wrap: wrap;
            }
            
            .control-group {
                flex: 1;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 AI 聊天助手</h1>
            <p>支持 OpenAI、Gemini 和 Vertex AI</p>
            <div class="provider-selector">
                <button class="provider-btn active" data-provider="openai" data-model="gpt-3.5-turbo">OpenAI</button>
                <button class="provider-btn" data-provider="gemini" data-model="gemini-pro">Gemini</button>
                <button class="provider-btn" data-provider="vertex-ai" data-model="gemini-1.5-pro">Vertex AI</button>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message system">
                欢迎使用 AI Proxy Server 聊天界面！请输入您的 API 密钥开始对话。
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="chat-input">
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea 
                        id="messageInput" 
                        placeholder="输入您的消息..."
                        rows="1"
                    ></textarea>
                    <button class="send-btn" id="sendBtn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="controls">
                <div class="control-group">
                    <label>API 密钥</label>
                    <input type="password" id="apiKey" placeholder="输入 API 密钥">
                </div>
                <div class="control-group">
                    <label>温度</label>
                    <input type="number" id="temperature" min="0" max="2" step="0.1" value="0.7">
                </div>
                <div class="control-group">
                    <label>最大令牌</label>
                    <input type="number" id="maxTokens" min="1" max="4000" value="500">
                </div>
                <div class="control-group">
                    <label>流式响应</label>
                    <div class="stream-toggle">
                        <input type="checkbox" id="streamMode" checked>
                        <span>启用</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="status-bar">
            <div class="connection-status">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">就绪</span>
            </div>
            <div id="statsText">消息: 0 | 令牌: 0</div>
        </div>
    </div>

    <script>
        class ChatInterface {
            constructor() {
                this.apiBaseUrl = 'http://localhost:3000';
                this.currentProvider = 'openai';
                this.currentModel = 'gpt-3.5-turbo';
                this.conversation = [];
                this.messageCount = 0;
                this.totalTokens = 0;
                
                this.initializeElements();
                this.bindEvents();
                this.checkServerStatus();
            }

            initializeElements() {
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.apiKeyInput = document.getElementById('apiKey');
                this.temperatureInput = document.getElementById('temperature');
                this.maxTokensInput = document.getElementById('maxTokens');
                this.streamModeInput = document.getElementById('streamMode');
                this.statusDot = document.getElementById('statusDot');
                this.statusText = document.getElementById('statusText');
                this.statsText = document.getElementById('statsText');
            }

            bindEvents() {
                // 发送按钮
                this.sendBtn.addEventListener('click', () => this.sendMessage());
                
                // 回车发送
                this.messageInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 自动调整输入框高度
                this.messageInput.addEventListener('input', () => {
                    this.messageInput.style.height = 'auto';
                    this.messageInput.style.height = this.messageInput.scrollHeight + 'px';
                });

                // 提供商选择
                document.querySelectorAll('.provider-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        document.querySelectorAll('.provider-btn').forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                        this.currentProvider = btn.dataset.provider;
                        this.currentModel = btn.dataset.model;
                        this.addMessage('system', `已切换到 ${btn.textContent} (${this.currentModel})`);
                    });
                });
            }

            async checkServerStatus() {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/health`);
                    if (response.ok) {
                        this.updateStatus('connected', '服务器连接正常');
                    } else {
                        this.updateStatus('disconnected', '服务器响应异常');
                    }
                } catch (error) {
                    this.updateStatus('disconnected', '无法连接到服务器');
                }
            }

            updateStatus(status, text) {
                this.statusDot.className = `status-dot ${status === 'connected' ? '' : 'disconnected'}`;
                this.statusText.textContent = text;
            }

            updateStats() {
                this.statsText.textContent = `消息: ${this.messageCount} | 令牌: ${this.totalTokens}`;
            }

            addMessage(role, content, streaming = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}`;
                
                if (streaming) {
                    messageDiv.id = 'streaming-message';
                }
                
                messageDiv.textContent = content;
                this.chatMessages.appendChild(messageDiv);
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                
                if (role !== 'system') {
                    this.messageCount++;
                    this.updateStats();
                }
                
                return messageDiv;
            }

            showTyping() {
                this.typingIndicator.style.display = 'block';
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            hideTyping() {
                this.typingIndicator.style.display = 'none';
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                const apiKey = this.apiKeyInput.value.trim();
                
                if (!message) return;
                if (!apiKey) {
                    this.addMessage('error', '请先输入 API 密钥');
                    return;
                }

                // 添加用户消息
                this.addMessage('user', message);
                this.conversation.push({ role: 'user', content: message });
                
                // 清空输入框
                this.messageInput.value = '';
                this.messageInput.style.height = 'auto';
                
                // 禁用发送按钮
                this.sendBtn.disabled = true;
                
                try {
                    if (this.streamModeInput.checked) {
                        await this.sendStreamingRequest(apiKey);
                    } else {
                        await this.sendNormalRequest(apiKey);
                    }
                } catch (error) {
                    this.addMessage('error', `请求失败: ${error.message}`);
                } finally {
                    this.sendBtn.disabled = false;
                    this.hideTyping();
                }
            }

            async sendNormalRequest(apiKey) {
                this.showTyping();
                
                const response = await fetch(`${this.apiBaseUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey
                    },
                    body: JSON.stringify({
                        model: this.currentModel,
                        messages: this.conversation,
                        temperature: parseFloat(this.temperatureInput.value),
                        max_tokens: parseInt(this.maxTokensInput.value),
                        provider: this.currentProvider
                    })
                });

                this.hideTyping();

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `HTTP ${response.status}`);
                }

                const data = await response.json();
                const assistantMessage = data.data.choices[0].message;
                
                this.addMessage('assistant', assistantMessage.content);
                this.conversation.push(assistantMessage);
                
                if (data.data.usage) {
                    this.totalTokens += data.data.usage.total_tokens;
                    this.updateStats();
                }
            }

            async sendStreamingRequest(apiKey) {
                const response = await fetch(`${this.apiBaseUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey,
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        model: this.currentModel,
                        messages: this.conversation,
                        temperature: parseFloat(this.temperatureInput.value),
                        max_tokens: parseInt(this.maxTokensInput.value),
                        provider: this.currentProvider,
                        stream: true
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `HTTP ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let assistantContent = '';
                let streamingMessageDiv = null;

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.trim() && line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                if (data.type === 'start') {
                                    streamingMessageDiv = this.addMessage('assistant', '', true);
                                } else if (data.choices && data.choices[0]) {
                                    const content = data.choices[0].delta?.content;
                                    if (content && streamingMessageDiv) {
                                        assistantContent += content;
                                        streamingMessageDiv.textContent = assistantContent;
                                        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                                    }
                                    
                                    if (data.choices[0].finish_reason) {
                                        if (streamingMessageDiv) {
                                            streamingMessageDiv.id = '';
                                        }
                                        this.conversation.push({ role: 'assistant', content: assistantContent });
                                    }
                                } else if (data.type === 'complete') {
                                    // 流式完成
                                    break;
                                }
                            } catch (e) {
                                // 忽略解析错误
                            }
                        }
                    }
                }
            }
        }

        // 初始化聊天界面
        document.addEventListener('DOMContentLoaded', () => {
            new ChatInterface();
        });
    </script>
</body>
</html>
