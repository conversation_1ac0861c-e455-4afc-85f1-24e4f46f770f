const axios = require('axios');
const EventSource = require('eventsource');

// 配置
const API_BASE_URL = 'http://localhost:3000';
const API_KEY = 'your-api-key-here'; // 替换为你的 API 密钥

/**
 * 流式聊天完成示例 (使用 fetch)
 */
async function streamingChatWithFetch() {
  console.log('🌊 开始流式聊天 (fetch)...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          { role: 'user', content: '请写一个关于人工智能的短故事。' }
        ],
        stream: true,
        temperature: 0.8,
        max_tokens: 500
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    console.log('📖 开始接收流式响应:\n');

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            
            if (data.type === 'start') {
              console.log(`🚀 开始生成 (${data.model})`);
            } else if (data.choices && data.choices[0]) {
              const content = data.choices[0].delta?.content;
              if (content) {
                process.stdout.write(content);
              }
              
              if (data.choices[0].finish_reason) {
                console.log(`\n\n✅ 生成完成 (${data.choices[0].finish_reason})`);
              }
            } else if (data.type === 'complete') {
              console.log(`\n📊 统计: ${data.chunkCount} 个数据块, 耗时 ${data.duration}`);
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    }
  } catch (error) {
    console.error('❌ 流式请求失败:', error.message);
  }
}

/**
 * 流式聊天完成示例 (使用 axios stream)
 */
async function streamingChatWithAxios() {
  console.log('\n🌊 开始流式聊天 (axios)...');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/v1/chat/completions`, {
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'user', content: '解释一下量子计算的基本原理。' }
      ],
      stream: true,
      temperature: 0.7,
      max_tokens: 400
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
        'Accept': 'text/event-stream'
      },
      responseType: 'stream'
    });

    console.log('📖 开始接收流式响应:\n');

    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      
      for (const line of lines) {
        if (line.trim() && line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            
            if (data.type === 'start') {
              console.log(`🚀 开始生成 (${data.model})`);
            } else if (data.choices && data.choices[0]) {
              const content = data.choices[0].delta?.content;
              if (content) {
                process.stdout.write(content);
              }
              
              if (data.choices[0].finish_reason) {
                console.log(`\n\n✅ 生成完成 (${data.choices[0].finish_reason})`);
              }
            } else if (data.type === 'complete') {
              console.log(`\n📊 统计: ${data.chunkCount} 个数据块, 耗时 ${data.duration}`);
            }
          } catch (e) {
            // 忽略解析错误
          }
        }
      }
    });

    response.data.on('end', () => {
      console.log('\n🏁 流式响应结束');
    });

    response.data.on('error', (error) => {
      console.error('❌ 流式响应错误:', error.message);
    });

    // 等待流结束
    await new Promise((resolve, reject) => {
      response.data.on('end', resolve);
      response.data.on('error', reject);
    });

  } catch (error) {
    console.error('❌ 流式请求失败:', error.response?.data || error.message);
  }
}

/**
 * 测试 SSE 连接示例
 */
function testSSEConnection() {
  console.log('\n🧪 测试 SSE 连接...');
  
  return new Promise((resolve, reject) => {
    const eventSource = new EventSource(`${API_BASE_URL}/v1/chat/test-stream`);
    let messageCount = 0;

    eventSource.onopen = () => {
      console.log('✅ SSE 连接已建立');
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        messageCount++;
        console.log(`📨 消息 ${messageCount}:`, data.message);
      } catch (e) {
        console.log('📨 原始消息:', event.data);
      }
    };

    eventSource.addEventListener('test', (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log(`🧪 测试事件:`, data);
      } catch (e) {
        console.log('🧪 测试事件 (原始):', event.data);
      }
    });

    eventSource.addEventListener('complete', (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log(`✅ 测试完成:`, data);
        eventSource.close();
        resolve();
      } catch (e) {
        console.log('✅ 测试完成');
        eventSource.close();
        resolve();
      }
    });

    eventSource.onerror = (error) => {
      console.error('❌ SSE 连接错误:', error);
      eventSource.close();
      reject(error);
    };

    // 超时保护
    setTimeout(() => {
      if (eventSource.readyState !== EventSource.CLOSED) {
        console.log('⏰ 测试超时，关闭连接');
        eventSource.close();
        resolve();
      }
    }, 10000);
  });
}

/**
 * 多模型流式比较
 */
async function compareStreamingModels() {
  console.log('\n🔍 比较不同模型的流式响应...');
  
  const models = [
    { name: 'GPT-3.5 Turbo', model: 'gpt-3.5-turbo', provider: 'openai' },
    { name: 'Gemini Pro', model: 'gemini-pro', provider: 'gemini' }
  ];

  const prompt = '用简洁的语言解释什么是区块链技术。';

  for (const config of models) {
    console.log(`\n🤖 ${config.name} 响应:`);
    console.log('─'.repeat(50));
    
    try {
      const response = await fetch(`${API_BASE_URL}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY,
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify({
          model: config.model,
          messages: [{ role: 'user', content: prompt }],
          stream: true,
          temperature: 0.7,
          max_tokens: 200,
          provider: config.provider
        })
      });

      if (!response.ok) {
        console.error(`❌ ${config.name} 请求失败: ${response.status}`);
        continue;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() && line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.choices && data.choices[0]) {
                const content = data.choices[0].delta?.content;
                if (content) {
                  process.stdout.write(content);
                }
                
                if (data.choices[0].finish_reason) {
                  console.log('\n');
                  break;
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } catch (error) {
      console.error(`❌ ${config.name} 流式请求失败:`, error.message);
    }
  }
}

/**
 * 流式聊天机器人示例
 */
async function streamingChatBot() {
  console.log('\n🤖 流式聊天机器人示例');
  console.log('输入 "quit" 退出聊天\n');

  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const conversation = [
    { role: 'system', content: '你是一个友好的助手，请用简洁明了的语言回答问题。' }
  ];

  const askQuestion = () => {
    rl.question('👤 你: ', async (input) => {
      if (input.toLowerCase() === 'quit') {
        console.log('👋 再见！');
        rl.close();
        return;
      }

      conversation.push({ role: 'user', content: input });

      try {
        console.log('🤖 助手: ');
        
        const response = await fetch(`${API_BASE_URL}/v1/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': API_KEY,
            'Accept': 'text/event-stream'
          },
          body: JSON.stringify({
            model: 'gpt-3.5-turbo',
            messages: conversation,
            stream: true,
            temperature: 0.8,
            max_tokens: 300
          })
        });

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let assistantReply = '';

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() && line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                
                if (data.choices && data.choices[0]) {
                  const content = data.choices[0].delta?.content;
                  if (content) {
                    process.stdout.write(content);
                    assistantReply += content;
                  }
                  
                  if (data.choices[0].finish_reason) {
                    console.log('\n');
                    conversation.push({ role: 'assistant', content: assistantReply });
                    askQuestion();
                    return;
                  }
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        }
      } catch (error) {
        console.error('❌ 请求失败:', error.message);
        askQuestion();
      }
    });
  };

  askQuestion();
}

// 主函数
async function main() {
  console.log('🌊 AI Proxy Server 流式响应示例\n');
  
  try {
    await testSSEConnection();
    await streamingChatWithFetch();
    await streamingChatWithAxios();
    await compareStreamingModels();
    
    // 注释掉交互式聊天机器人，避免阻塞
    // await streamingChatBot();
    
    console.log('\n✨ 所有流式示例运行完成！');
  } catch (error) {
    console.error('❌ 示例运行失败:', error);
  }
}

// 运行示例
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  streamingChatWithFetch,
  streamingChatWithAxios,
  testSSEConnection,
  compareStreamingModels,
  streamingChatBot
};
