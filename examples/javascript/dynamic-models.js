const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000';
const API_KEY = 'your-api-key-here'; // 替换为你的 API 密钥

/**
 * 动态模型配置示例
 */
class DynamicModelExample {
  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY
      }
    });
  }

  /**
   * 获取所有动态模型
   */
  async getDynamicModels() {
    try {
      console.log('📋 获取动态模型列表...');
      
      const response = await this.client.get('/v1/models/dynamic');
      const models = response.data.data;
      
      console.log(`✅ 找到 ${models.length} 个动态模型:`);
      models.forEach(model => {
        console.log(`  - ${model.name} (${model.provider})`);
        console.log(`    实际模型: ${model.actualModel}`);
        console.log(`    基础URL: ${model.baseUrl}`);
        console.log(`    状态: ${model.isActive ? '激活' : '停用'}`);
        console.log(`    支持视觉: ${model.supportsVision ? '是' : '否'}`);
        console.log('');
      });
      
      return models;
    } catch (error) {
      console.error('❌ 获取动态模型失败:', error.response?.data || error.message);
      return [];
    }
  }

  /**
   * 使用指定的动态模型进行聊天
   */
  async chatWithModel(modelName, message) {
    try {
      console.log(`🤖 使用模型 "${modelName}" 进行对话...`);
      console.log(`💬 用户: ${message}`);
      
      const response = await this.client.post('/v1/chat/completions', {
        model: modelName, // 直接使用动态模型名称
        messages: [
          { role: 'user', content: message }
        ],
        temperature: 0.7,
        max_tokens: 200
      });

      const result = response.data.data;
      const reply = result.choices[0].message.content;
      
      console.log(`🤖 ${modelName}: ${reply}`);
      console.log(`📊 令牌使用: ${result.usage.total_tokens}`);
      console.log('');
      
      return reply;
    } catch (error) {
      console.error(`❌ 使用模型 ${modelName} 聊天失败:`, error.response?.data || error.message);
      return null;
    }
  }

  /**
   * 比较不同动态模型的响应
   */
  async compareModels(message) {
    console.log('🔍 比较不同动态模型的响应...');
    console.log(`📝 问题: ${message}\n`);
    
    const models = await this.getDynamicModels();
    const activeModels = models.filter(model => model.isActive);
    
    if (activeModels.length === 0) {
      console.log('❌ 没有激活的动态模型');
      return;
    }

    for (const model of activeModels) {
      await this.chatWithModel(model.name, message);
    }
  }

  /**
   * 流式聊天示例
   */
  async streamChatWithModel(modelName, message) {
    try {
      console.log(`🌊 使用模型 "${modelName}" 进行流式对话...`);
      console.log(`💬 用户: ${message}`);
      console.log(`🤖 ${modelName}: `, '');
      
      const response = await this.client.post('/v1/chat/completions', {
        model: modelName,
        messages: [
          { role: 'user', content: message }
        ],
        stream: true,
        temperature: 0.8,
        max_tokens: 300
      }, {
        headers: {
          'Accept': 'text/event-stream'
        },
        responseType: 'stream'
      });

      return new Promise((resolve, reject) => {
        let fullResponse = '';
        
        response.data.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          
          for (const line of lines) {
            if (line.trim() && line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                
                if (data.choices && data.choices[0]) {
                  const content = data.choices[0].delta?.content;
                  if (content) {
                    process.stdout.write(content);
                    fullResponse += content;
                  }
                  
                  if (data.choices[0].finish_reason) {
                    console.log('\n');
                    resolve(fullResponse);
                  }
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        });

        response.data.on('error', (error) => {
          console.error('\n❌ 流式响应错误:', error.message);
          reject(error);
        });
      });
    } catch (error) {
      console.error(`❌ 流式聊天失败:`, error.response?.data || error.message);
      return null;
    }
  }

  /**
   * 测试模型可用性
   */
  async testModelAvailability() {
    console.log('🧪 测试动态模型可用性...\n');
    
    const models = await this.getDynamicModels();
    
    for (const model of models) {
      if (!model.isActive) {
        console.log(`⏸️  ${model.name}: 已停用`);
        continue;
      }

      try {
        const startTime = Date.now();
        await this.chatWithModel(model.name, 'ping');
        const duration = Date.now() - startTime;
        console.log(`✅ ${model.name}: 可用 (${duration}ms)`);
      } catch (error) {
        console.log(`❌ ${model.name}: 不可用 - ${error.message}`);
      }
    }
  }

  /**
   * 演示多轮对话
   */
  async multiTurnConversation(modelName) {
    console.log(`🔄 使用 "${modelName}" 进行多轮对话...\n`);
    
    const conversation = [
      { role: 'system', content: '你是一个有用的助手。' }
    ];

    const questions = [
      '你好，请介绍一下自己。',
      '你能帮我解释什么是人工智能吗？',
      '那机器学习和深度学习有什么区别？'
    ];

    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      conversation.push({ role: 'user', content: question });
      
      console.log(`👤 问题 ${i + 1}: ${question}`);
      
      try {
        const response = await this.client.post('/v1/chat/completions', {
          model: modelName,
          messages: conversation,
          temperature: 0.7,
          max_tokens: 200
        });

        const reply = response.data.data.choices[0].message;
        conversation.push(reply);
        
        console.log(`🤖 ${modelName}: ${reply.content}\n`);
      } catch (error) {
        console.error(`❌ 对话失败:`, error.response?.data || error.message);
        break;
      }
    }
  }

  /**
   * 获取模型统计信息
   */
  async getModelStats() {
    try {
      console.log('📊 获取模型统计信息...');
      
      const response = await this.client.get('/v1/models/stats');
      const stats = response.data.data;
      
      console.log('✅ 模型统计:');
      console.log(`  总模型数: ${stats.total}`);
      console.log(`  按提供商分布:`);
      Object.entries(stats.byProvider).forEach(([provider, count]) => {
        console.log(`    ${provider}: ${count} 个模型`);
      });
      console.log('');
      
      return stats;
    } catch (error) {
      console.error('❌ 获取模型统计失败:', error.response?.data || error.message);
      return null;
    }
  }
}

// 主函数
async function main() {
  console.log('🚀 AI Proxy Server 动态模型示例\n');
  
  const example = new DynamicModelExample();
  
  try {
    // 获取动态模型列表
    await example.getDynamicModels();
    
    // 获取统计信息
    await example.getModelStats();
    
    // 测试模型可用性
    await example.testModelAvailability();
    
    // 比较不同模型的响应
    await example.compareModels('请用一句话解释什么是区块链技术。');
    
    // 流式对话示例
    await example.streamChatWithModel('夏目官方模型1', '请写一个关于春天的短诗。');
    
    // 多轮对话示例
    await example.multiTurnConversation('夏目官方模型2');
    
    console.log('✨ 所有动态模型示例运行完成！');
  } catch (error) {
    console.error('❌ 示例运行失败:', error);
  }
}

// 运行示例
if (require.main === module) {
  main().catch(console.error);
}

module.exports = DynamicModelExample;
