const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000';
const API_KEY = 'your-api-key-here'; // 替换为你的 API 密钥

/**
 * 基础聊天完成示例
 */
async function basicChatCompletion() {
  try {
    console.log('🤖 发送聊天请求...');
    
    const response = await axios.post(`${API_BASE_URL}/v1/chat/completions`, {
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: '你是一个有用的助手。' },
        { role: 'user', content: '请介绍一下人工智能的发展历史。' }
      ],
      temperature: 0.7,
      max_tokens: 500
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY
      }
    });

    const result = response.data.data;
    console.log('✅ 响应成功:');
    console.log('模型:', result.model);
    console.log('内容:', result.choices[0].message.content);
    console.log('令牌使用:', result.usage);
    
  } catch (error) {
    console.error('❌ 请求失败:', error.response?.data || error.message);
  }
}

/**
 * 多轮对话示例
 */
async function multiTurnConversation() {
  const conversation = [
    { role: 'system', content: '你是一个编程助手。' },
    { role: 'user', content: '什么是 JavaScript 闭包？' }
  ];

  try {
    console.log('\n🔄 开始多轮对话...');
    
    // 第一轮
    let response = await axios.post(`${API_BASE_URL}/v1/chat/completions`, {
      model: 'gpt-3.5-turbo',
      messages: conversation,
      temperature: 0.7,
      max_tokens: 300
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY
      }
    });

    const assistantReply = response.data.data.choices[0].message;
    conversation.push(assistantReply);
    
    console.log('🤖 助手:', assistantReply.content);

    // 第二轮
    conversation.push({ role: 'user', content: '能给我一个简单的例子吗？' });
    
    response = await axios.post(`${API_BASE_URL}/v1/chat/completions`, {
      model: 'gpt-3.5-turbo',
      messages: conversation,
      temperature: 0.7,
      max_tokens: 300
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY
      }
    });

    const secondReply = response.data.data.choices[0].message;
    console.log('🤖 助手:', secondReply.content);
    
  } catch (error) {
    console.error('❌ 对话失败:', error.response?.data || error.message);
  }
}

/**
 * 不同提供商比较示例
 */
async function compareProviders() {
  const prompt = '请用一句话解释什么是机器学习。';
  const providers = [
    { name: 'OpenAI', model: 'gpt-3.5-turbo', provider: 'openai' },
    { name: 'Gemini', model: 'gemini-pro', provider: 'gemini' }
  ];

  console.log('\n🔍 比较不同提供商的响应...');
  console.log('问题:', prompt);

  for (const config of providers) {
    try {
      const response = await axios.post(`${API_BASE_URL}/v1/chat/completions`, {
        model: config.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 100,
        provider: config.provider
      }, {
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': API_KEY
        }
      });

      const result = response.data.data;
      console.log(`\n${config.name} (${config.model}):`);
      console.log(result.choices[0].message.content);
      console.log('令牌:', result.usage.total_tokens);
      
    } catch (error) {
      console.error(`❌ ${config.name} 请求失败:`, error.response?.data?.error || error.message);
    }
  }
}

/**
 * 批量请求示例
 */
async function batchRequests() {
  try {
    console.log('\n📦 发送批量请求...');
    
    const response = await axios.post(`${API_BASE_URL}/v1/chat/batch`, {
      requests: [
        {
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: '什么是 AI？' }],
          max_tokens: 50
        },
        {
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: '什么是机器学习？' }],
          max_tokens: 50
        },
        {
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: '什么是深度学习？' }],
          max_tokens: 50
        }
      ]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY
      }
    });

    const results = response.data.data.responses;
    console.log(`✅ 批量请求完成，共 ${results.length} 个响应:`);
    
    results.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.choices[0].message.content}`);
    });
    
  } catch (error) {
    console.error('❌ 批量请求失败:', error.response?.data || error.message);
  }
}

/**
 * 错误处理示例
 */
async function errorHandlingExample() {
  console.log('\n🚨 错误处理示例...');
  
  // 测试无效的 API 密钥
  try {
    await axios.post(`${API_BASE_URL}/v1/chat/completions`, {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: 'Hello' }]
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'invalid-key'
      }
    });
  } catch (error) {
    if (error.response) {
      console.log('❌ API 错误:', error.response.status, error.response.data.error);
    } else {
      console.log('❌ 网络错误:', error.message);
    }
  }

  // 测试无效的请求参数
  try {
    await axios.post(`${API_BASE_URL}/v1/chat/completions`, {
      model: '', // 空模型名
      messages: [] // 空消息数组
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY
      }
    });
  } catch (error) {
    console.log('❌ 验证错误:', error.response?.data?.error);
  }
}

/**
 * 获取模型列表示例
 */
async function getModels() {
  try {
    console.log('\n📋 获取模型列表...');
    
    const response = await axios.get(`${API_BASE_URL}/v1/models`, {
      headers: {
        'X-API-Key': API_KEY
      }
    });

    const models = response.data.data.data;
    console.log(`✅ 共找到 ${models.length} 个模型:`);
    
    models.slice(0, 5).forEach(model => {
      console.log(`- ${model.id} (${model.provider})`);
    });
    
    if (models.length > 5) {
      console.log(`... 还有 ${models.length - 5} 个模型`);
    }
    
  } catch (error) {
    console.error('❌ 获取模型列表失败:', error.response?.data || error.message);
  }
}

/**
 * 健康检查示例
 */
async function healthCheck() {
  try {
    console.log('\n🏥 健康检查...');
    
    const response = await axios.get(`${API_BASE_URL}/health/detailed`);
    const health = response.data.data;
    
    console.log('✅ 服务状态:', health.status);
    console.log('运行时间:', Math.floor(health.uptime / 1000), '秒');
    
    Object.entries(health.services).forEach(([service, status]) => {
      const emoji = status.status === 'up' ? '✅' : '❌';
      console.log(`${emoji} ${service}: ${status.status}`);
    });
    
  } catch (error) {
    console.error('❌ 健康检查失败:', error.response?.data || error.message);
  }
}

// 主函数
async function main() {
  console.log('🚀 AI Proxy Server JavaScript 示例\n');
  
  // 运行所有示例
  await healthCheck();
  await getModels();
  await basicChatCompletion();
  await multiTurnConversation();
  await compareProviders();
  await batchRequests();
  await errorHandlingExample();
  
  console.log('\n✨ 所有示例运行完成！');
}

// 运行示例
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  basicChatCompletion,
  multiTurnConversation,
  compareProviders,
  batchRequests,
  errorHandlingExample,
  getModels,
  healthCheck
};
