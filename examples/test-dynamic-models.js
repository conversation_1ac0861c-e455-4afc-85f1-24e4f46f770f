#!/usr/bin/env node

/**
 * 动态模型功能测试脚本
 * 用于验证动态模型配置和使用功能
 */

const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000';
const API_KEY = 'your-api-key-here'; // 替换为你的 API 密钥

const client = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': API_KEY
  }
});

// 测试用例
const testCases = [
  {
    name: '夏目官方模型1',
    message: '你好，请介绍一下自己。'
  },
  {
    name: '夏目官方模型2', 
    message: '什么是人工智能？'
  },
  {
    name: '需科学上网',
    message: '请解释一下机器学习的基本概念。'
  }
];

/**
 * 测试健康检查
 */
async function testHealthCheck() {
  console.log('🏥 测试服务健康状态...');
  
  try {
    const response = await client.get('/health');
    console.log('✅ 服务健康状态正常');
    return true;
  } catch (error) {
    console.error('❌ 服务不可用:', error.message);
    return false;
  }
}

/**
 * 测试获取动态模型列表
 */
async function testGetDynamicModels() {
  console.log('\n📋 测试获取动态模型列表...');
  
  try {
    const response = await client.get('/v1/models/dynamic');
    const models = response.data.data;
    
    console.log(`✅ 成功获取 ${models.length} 个动态模型:`);
    models.forEach(model => {
      console.log(`  - ${model.name} (${model.provider}) - ${model.isActive ? '激活' : '停用'}`);
    });
    
    return models;
  } catch (error) {
    console.error('❌ 获取动态模型列表失败:', error.response?.data || error.message);
    return [];
  }
}

/**
 * 测试使用动态模型进行聊天
 */
async function testChatWithDynamicModel(modelName, message) {
  console.log(`\n🤖 测试使用 "${modelName}" 进行聊天...`);
  console.log(`💬 消息: ${message}`);
  
  try {
    const startTime = Date.now();
    
    const response = await client.post('/v1/chat/completions', {
      model: modelName,
      messages: [
        { role: 'user', content: message }
      ],
      temperature: 0.7,
      max_tokens: 100
    });

    const duration = Date.now() - startTime;
    const result = response.data.data;
    const reply = result.choices[0].message.content;
    
    console.log(`✅ 响应成功 (${duration}ms):`);
    console.log(`🤖 回复: ${reply}`);
    console.log(`📊 令牌: ${result.usage.total_tokens}`);
    
    return true;
  } catch (error) {
    console.error(`❌ 聊天失败:`, error.response?.data?.error || error.message);
    return false;
  }
}

/**
 * 测试流式聊天
 */
async function testStreamChat(modelName, message) {
  console.log(`\n🌊 测试使用 "${modelName}" 进行流式聊天...`);
  console.log(`💬 消息: ${message}`);
  console.log(`🤖 流式回复: `);
  
  try {
    const response = await client.post('/v1/chat/completions', {
      model: modelName,
      messages: [
        { role: 'user', content: message }
      ],
      stream: true,
      temperature: 0.8,
      max_tokens: 150
    }, {
      headers: {
        'Accept': 'text/event-stream'
      },
      responseType: 'stream'
    });

    return new Promise((resolve, reject) => {
      let hasContent = false;
      
      response.data.on('data', (chunk) => {
        const lines = chunk.toString().split('\n');
        
        for (const line of lines) {
          if (line.trim() && line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.choices && data.choices[0]) {
                const content = data.choices[0].delta?.content;
                if (content) {
                  process.stdout.write(content);
                  hasContent = true;
                }
                
                if (data.choices[0].finish_reason) {
                  console.log('\n✅ 流式聊天成功');
                  resolve(hasContent);
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      });

      response.data.on('error', (error) => {
        console.error('\n❌ 流式聊天失败:', error.message);
        reject(error);
      });

      // 超时保护
      setTimeout(() => {
        if (!hasContent) {
          console.log('\n⏰ 流式聊天超时');
          resolve(false);
        }
      }, 30000);
    });
  } catch (error) {
    console.error(`❌ 流式聊天失败:`, error.response?.data?.error || error.message);
    return false;
  }
}

/**
 * 测试模型验证
 */
async function testModelValidation() {
  console.log('\n✅ 测试模型验证...');
  
  const testModels = ['夏目官方模型1', 'gpt-3.5-turbo', 'non-existent-model'];
  
  for (const model of testModels) {
    try {
      const response = await client.post('/v1/models/validate', {
        model: model
      });
      
      const result = response.data.data;
      console.log(`  ${model}: ${result.supported ? '✅ 支持' : '❌ 不支持'}`);
    } catch (error) {
      console.log(`  ${model}: ❌ 验证失败`);
    }
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🧪 开始动态模型功能测试\n');
  
  // 测试服务健康状态
  const isHealthy = await testHealthCheck();
  if (!isHealthy) {
    console.log('❌ 服务不可用，停止测试');
    return;
  }

  // 获取动态模型列表
  const models = await testGetDynamicModels();
  if (models.length === 0) {
    console.log('❌ 没有可用的动态模型，停止测试');
    return;
  }

  // 测试模型验证
  await testModelValidation();

  // 测试每个激活的动态模型
  const activeModels = models.filter(model => model.isActive);
  
  if (activeModels.length === 0) {
    console.log('❌ 没有激活的动态模型');
    return;
  }

  console.log(`\n🎯 开始测试 ${activeModels.length} 个激活的动态模型...\n`);

  let successCount = 0;
  let totalTests = 0;

  for (const model of activeModels) {
    // 找到对应的测试用例
    const testCase = testCases.find(tc => tc.name === model.name);
    const message = testCase ? testCase.message : '你好，这是一个测试消息。';
    
    // 测试普通聊天
    totalTests++;
    const chatSuccess = await testChatWithDynamicModel(model.name, message);
    if (chatSuccess) successCount++;
    
    // 测试流式聊天
    totalTests++;
    const streamSuccess = await testStreamChat(model.name, '请简单介绍一下你的能力。');
    if (streamSuccess) successCount++;
    
    // 添加延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log(`  总测试数: ${totalTests}`);
  console.log(`  成功数: ${successCount}`);
  console.log(`  失败数: ${totalTests - successCount}`);
  console.log(`  成功率: ${((successCount / totalTests) * 100).toFixed(1)}%`);
  
  if (successCount === totalTests) {
    console.log('\n🎉 所有测试通过！动态模型功能正常工作。');
  } else {
    console.log('\n⚠️  部分测试失败，请检查配置和网络连接。');
  }
}

// 主函数
async function main() {
  try {
    await runAllTests();
  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}
