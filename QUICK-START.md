# 🚀 AI Proxy Server 快速启动指南

本指南将帮助您在几分钟内启动和运行 AI Proxy Server。

## 📋 前置要求

- **Node.js 18+** 
- **npm 8+**
- **Git**

## ⚡ 一键设置

### macOS/Linux

```bash
# 克隆项目
git clone <repository-url>
cd ai-proxy-server

# 运行自动设置脚本
npm run setup
```

### Windows

```cmd
# 克隆项目
git clone <repository-url>
cd ai-proxy-server

# 运行自动设置脚本
npm run setup:win
```

## 🔧 手动设置

如果自动设置失败，可以手动执行以下步骤：

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

**重要配置项**：
```env
# 服务器配置
PORT=3000
NODE_ENV=development

# OpenAI API 配置（必需）
OPENAI_API_KEY=your_openai_api_key_here

# JWT 密钥（必需）
JWT_SECRET=your_jwt_secret_here

# Gemini API 配置（可选，已有预配置模型）
GEMINI_API_KEY=your_gemini_api_key_here
```

### 3. 构建项目

```bash
npm run build
```

### 4. 启动服务

```bash
# 开发模式（推荐）
npm run dev

# 或生产模式
npm start
```

## 🎯 预配置的动态模型

项目已预配置了以下模型，无需额外设置：

| 模型名称 | 提供商 | 描述 |
|---------|--------|------|
| 夏目官方模型1 | OpenAI 兼容 | 通义千问视觉大模型 |
| 夏目官方模型2 | OpenAI 兼容 | Gemini 2.5 Pro |
| 需科学上网 | OpenAI 兼容 | Gemini 2.5 Pro（需VPN） |
| Gemini Bearer Token | Gemini | Bearer Token 认证 |
| Gemini 官方 | Gemini | 官方 API |
| Gemini 备用 | Gemini | 备用地址 |

## 🧪 测试功能

### 1. 健康检查

```bash
curl http://localhost:3000/health
```

### 2. 获取动态模型列表

```bash
curl http://localhost:3000/v1/models/dynamic
```

### 3. 测试聊天功能

```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "model": "夏目官方模型1",
    "messages": [
      {"role": "user", "content": "你好！"}
    ]
  }'
```

### 4. 运行自动化测试

```bash
# 测试动态模型功能
npm run test:dynamic

# 运行 JavaScript 示例
npm run example:js

# 运行 cURL 示例
npm run example:curl
```

## 🐛 VS Code 调试

### 1. 打开项目

```bash
code .
```

### 2. 安装推荐扩展

VS Code 会自动提示安装推荐的扩展。

### 3. 开始调试

- 按 `F5` 或点击调试面板的"启动 AI Proxy Server (开发模式)"
- 设置断点进行调试
- 使用集成终端查看日志

### 4. 可用的调试配置

- **启动 AI Proxy Server (开发模式)** - 开发环境调试
- **启动 AI Proxy Server (生产模式)** - 生产环境调试
- **运行测试** - 调试测试用例
- **调试动态模型测试** - 调试动态模型功能
- **运行 JavaScript 示例** - 调试示例代码

## 🐳 Docker 部署

### 快速启动

```bash
# 使用 Docker Compose（推荐）
docker-compose up -d

# 查看日志
docker-compose logs -f ai-proxy

# 停止服务
docker-compose down
```

### 手动 Docker 部署

```bash
# 构建镜像
npm run docker:build

# 运行容器
npm run docker:run
```

## 📱 前端界面

打开浏览器访问：
```
http://localhost:3000/examples/frontend/chat-interface.html
```

这是一个完整的聊天界面，支持：
- 动态模型选择
- 流式和非流式响应
- 参数调整
- 实时状态显示

## 🔧 常用命令

```bash
# 开发相关
npm run dev          # 开发模式启动
npm run build        # 构建项目
npm start            # 生产模式启动
npm test             # 运行测试

# 示例和测试
npm run test:dynamic # 测试动态模型
npm run example:js   # JavaScript 示例
npm run example:curl # cURL 示例

# 代码质量
npm run lint         # 代码检查
npm run lint:fix     # 自动修复
npm run format       # 代码格式化

# Docker
npm run docker:build # 构建 Docker 镜像
npm run docker:run   # 运行 Docker 容器

# 清理
npm run clean        # 清理构建文件
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 修改 .env 文件中的 PORT
   PORT=3001
   ```

2. **依赖安装失败**
   ```bash
   # 清理缓存重新安装
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **TypeScript 编译错误**
   ```bash
   # 清理并重新构建
   npm run clean
   npm run build
   ```

4. **API 密钥错误**
   - 检查 `.env` 文件中的 API 密钥是否正确
   - 确保 API 密钥有足够的权限

### 获取帮助

- 查看 [完整文档](README.md)
- 查看 [动态模型文档](docs/DYNAMIC-MODELS.md)
- 查看 [API 文档](docs/API.md)
- 提交 [GitHub Issue](https://github.com/your-repo/issues)

## 🎉 下一步

项目启动成功后，您可以：

1. **测试 API** - 使用 Postman 或 curl 测试各种端点
2. **集成前端** - 将 API 集成到您的前端应用
3. **添加模型** - 通过 API 添加更多动态模型
4. **部署生产** - 使用 Docker 部署到生产环境
5. **监控日志** - 查看 `logs/` 目录下的日志文件

祝您使用愉快！🚀
