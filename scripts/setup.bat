@echo off
setlocal enabledelayedexpansion

REM AI Proxy Server 快速设置脚本 (Windows)
REM 用于快速安装依赖和配置项目

echo [INFO] 开始设置 AI Proxy Server...

REM 检查 Node.js 版本
echo.
echo === 检查 Node.js 版本 ===
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js 未安装，请先安装 Node.js 18+
    pause
    exit /b 1
)

for /f "tokens=1 delims=v" %%i in ('node -v') do set NODE_VERSION=%%i
for /f "tokens=1 delims=." %%i in ("%NODE_VERSION:~1%") do set MAJOR_VERSION=%%i

if %MAJOR_VERSION% lss 18 (
    echo [ERROR] Node.js 版本过低，需要 18+
    pause
    exit /b 1
)

echo [INFO] Node.js 版本检查通过: %NODE_VERSION%

REM 检查 npm 版本
echo.
echo === 检查 npm 版本 ===
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] npm 未安装
    pause
    exit /b 1
)

for /f %%i in ('npm -v') do set NPM_VERSION=%%i
echo [INFO] npm 版本: %NPM_VERSION%

REM 安装依赖
echo.
echo === 安装项目依赖 ===
if exist package-lock.json (
    echo [INFO] 使用 npm ci 安装依赖...
    npm ci
) else (
    echo [INFO] 使用 npm install 安装依赖...
    npm install
)

if %errorlevel% neq 0 (
    echo [ERROR] 依赖安装失败
    pause
    exit /b 1
)

echo [INFO] 依赖安装完成

REM 设置环境变量
echo.
echo === 设置环境变量 ===
if not exist .env (
    if exist .env.example (
        copy .env.example .env >nul
        echo [INFO] 已创建 .env 文件，请编辑其中的配置
        echo [WARNING] 请在 .env 文件中设置必要的 API 密钥
    ) else (
        echo [ERROR] .env.example 文件不存在
        pause
        exit /b 1
    )
) else (
    echo [INFO] .env 文件已存在
)

REM 创建必要的目录
echo.
echo === 创建必要的目录 ===
if not exist logs mkdir logs
if not exist dist mkdir dist
echo [INFO] 目录创建完成

REM 构建项目
echo.
echo === 构建项目 ===
echo [INFO] 编译 TypeScript...
npm run build

if %errorlevel% neq 0 (
    echo [ERROR] 项目构建失败
    pause
    exit /b 1
)

echo [INFO] 项目构建完成

REM 运行测试
if "%1"=="--skip-tests" goto skip_tests
echo.
echo === 运行测试 ===
echo [INFO] 运行单元测试...
npm test

if %errorlevel% neq 0 (
    echo [WARNING] 测试失败，但继续执行
) else (
    echo [INFO] 测试完成
)

:skip_tests

REM 显示使用说明
echo.
echo === 使用说明 ===
echo 项目设置完成！以下是常用命令：
echo.
echo 开发模式启动:
echo   npm run dev
echo.
echo 生产模式启动:
echo   npm start
echo.
echo 运行测试:
echo   npm test
echo.
echo 测试动态模型:
echo   node examples/test-dynamic-models.js
echo.
echo Docker 部署:
echo   docker-compose up -d
echo.
echo VS Code 调试:
echo   按 F5 或使用调试面板
echo.
echo [INFO] 更多信息请查看 README.md

echo.
echo [INFO] 设置完成！🎉
pause
