#!/bin/bash

# AI Proxy Server 快速设置脚本
# 用于快速安装依赖和配置项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 检查 Node.js 版本
check_node_version() {
    print_step "检查 Node.js 版本"
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js 版本过低 (当前: $(node -v))，需要 18+"
        exit 1
    fi
    
    print_message "Node.js 版本检查通过: $(node -v)"
}

# 检查 npm 版本
check_npm_version() {
    print_step "检查 npm 版本"
    
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装"
        exit 1
    fi
    
    print_message "npm 版本: $(npm -v)"
}

# 安装依赖
install_dependencies() {
    print_step "安装项目依赖"
    
    if [ -f "package-lock.json" ]; then
        print_message "使用 npm ci 安装依赖..."
        npm ci
    else
        print_message "使用 npm install 安装依赖..."
        npm install
    fi
    
    print_message "依赖安装完成"
}

# 设置环境变量
setup_environment() {
    print_step "设置环境变量"
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_message "已创建 .env 文件，请编辑其中的配置"
            print_warning "请在 .env 文件中设置必要的 API 密钥"
        else
            print_error ".env.example 文件不存在"
            exit 1
        fi
    else
        print_message ".env 文件已存在"
    fi
}

# 创建必要的目录
create_directories() {
    print_step "创建必要的目录"
    
    mkdir -p logs
    mkdir -p dist
    
    print_message "目录创建完成"
}

# 构建项目
build_project() {
    print_step "构建项目"
    
    print_message "编译 TypeScript..."
    npm run build
    
    print_message "项目构建完成"
}

# 运行测试
run_tests() {
    print_step "运行测试"
    
    if [ "$1" = "--skip-tests" ]; then
        print_warning "跳过测试"
        return
    fi
    
    print_message "运行单元测试..."
    npm test
    
    print_message "测试完成"
}

# 检查服务健康状态
check_health() {
    print_step "检查服务健康状态"
    
    print_message "启动服务进行健康检查..."
    
    # 在后台启动服务
    npm start &
    SERVER_PID=$!
    
    # 等待服务启动
    sleep 5
    
    # 检查健康状态
    if curl -s http://localhost:3000/health > /dev/null; then
        print_message "服务健康检查通过"
    else
        print_warning "服务健康检查失败，请检查配置"
    fi
    
    # 停止服务
    kill $SERVER_PID 2>/dev/null || true
    wait $SERVER_PID 2>/dev/null || true
}

# 显示使用说明
show_usage() {
    print_step "使用说明"
    
    echo "项目设置完成！以下是常用命令："
    echo ""
    echo "开发模式启动:"
    echo "  npm run dev"
    echo ""
    echo "生产模式启动:"
    echo "  npm start"
    echo ""
    echo "运行测试:"
    echo "  npm test"
    echo ""
    echo "测试动态模型:"
    echo "  node examples/test-dynamic-models.js"
    echo ""
    echo "Docker 部署:"
    echo "  docker-compose up -d"
    echo ""
    echo "VS Code 调试:"
    echo "  按 F5 或使用调试面板"
    echo ""
    print_message "更多信息请查看 README.md"
}

# 主函数
main() {
    print_message "开始设置 AI Proxy Server..."
    
    check_node_version
    check_npm_version
    install_dependencies
    setup_environment
    create_directories
    build_project
    run_tests "$1"
    
    if [ "$1" != "--skip-health" ]; then
        check_health
    fi
    
    show_usage
    
    print_message "设置完成！🎉"
}

# 解析命令行参数
SKIP_TESTS=false
SKIP_HEALTH=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-health)
            SKIP_HEALTH=true
            shift
            ;;
        --help|-h)
            echo "AI Proxy Server 设置脚本"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --skip-tests    跳过测试"
            echo "  --skip-health   跳过健康检查"
            echo "  --help, -h      显示帮助信息"
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 运行主函数
if [ "$SKIP_TESTS" = true ]; then
    main --skip-tests
elif [ "$SKIP_HEALTH" = true ]; then
    main --skip-health
else
    main
fi
