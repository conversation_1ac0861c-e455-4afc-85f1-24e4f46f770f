import request from 'supertest';
import { createApp } from '@/app';
import { Express } from 'express';

describe('API Integration Tests', () => {
  let app: Express;

  beforeAll(() => {
    app = createApp();
  });

  describe('Health Endpoints', () => {
    describe('GET /health', () => {
      it('should return health status', async () => {
        const response = await request(app)
          .get('/health')
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          data: {
            status: 'healthy',
            version: '1.0.0',
          },
        });
      });
    });

    describe('GET /health/ping', () => {
      it('should return ping response', async () => {
        const response = await request(app)
          .get('/health/ping')
          .expect(200);

        expect(response.body).toMatchObject({
          status: 'ok',
        });
      });
    });

    describe('GET /health/detailed', () => {
      it('should return detailed health status', async () => {
        const response = await request(app)
          .get('/health/detailed')
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          data: {
            status: expect.any(String),
            services: expect.any(Object),
          },
        });
      });
    });
  });

  describe('Models Endpoints', () => {
    describe('GET /v1/models', () => {
      it('should return models list', async () => {
        const response = await request(app)
          .get('/v1/models')
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          data: {
            object: 'list',
            data: expect.any(Array),
            pagination: expect.any(Object),
          },
        });
      });

      it('should support pagination', async () => {
        const response = await request(app)
          .get('/v1/models?page=1&limit=5')
          .expect(200);

        expect(response.body.data.pagination).toMatchObject({
          page: 1,
          limit: 5,
        });
      });

      it('should support provider filtering', async () => {
        const response = await request(app)
          .get('/v1/models?provider=openai')
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
        });
      });
    });

    describe('GET /v1/models/:id', () => {
      it('should return model details for valid model', async () => {
        const response = await request(app)
          .get('/v1/models/gpt-3.5-turbo')
          .expect(200);

        expect(response.body).toMatchObject({
          success: true,
          data: {
            id: 'gpt-3.5-turbo',
            object: 'model',
            provider: expect.any(String),
          },
        });
      });

      it('should return 404 for invalid model', async () => {
        await request(app)
          .get('/v1/models/invalid-model')
          .expect(404);
      });
    });
  });

  describe('Chat Endpoints', () => {
    const validChatRequest = {
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'user', content: 'Hello, world!' },
      ],
      temperature: 0.7,
      max_tokens: 100,
    };

    describe('POST /v1/chat/completions', () => {
      it('should require authentication', async () => {
        await request(app)
          .post('/v1/chat/completions')
          .send(validChatRequest)
          .expect(401);
      });

      it('should validate request body', async () => {
        const invalidRequest = {
          model: '',
          messages: [],
        };

        await request(app)
          .post('/v1/chat/completions')
          .set('X-API-Key', 'test-api-key-12345678901234567890')
          .send(invalidRequest)
          .expect(400);
      });

      it('should handle valid request with mock API key', async () => {
        // 注意：这个测试需要模拟 AI 服务
        const response = await request(app)
          .post('/v1/chat/completions')
          .set('X-API-Key', 'test-api-key-12345678901234567890')
          .send(validChatRequest);

        // 由于我们没有真实的 API 密钥，这里可能会失败
        // 在实际测试中，应该模拟 AI 服务的响应
        expect([200, 401, 500]).toContain(response.status);
      });
    });

    describe('GET /v1/chat/test-stream', () => {
      it('should support SSE', async () => {
        const response = await request(app)
          .get('/v1/chat/test-stream')
          .set('Accept', 'text/event-stream')
          .expect(200);

        expect(response.headers['content-type']).toContain('text/event-stream');
      });

      it('should reject non-SSE requests', async () => {
        await request(app)
          .get('/v1/chat/test-stream')
          .set('Accept', 'application/json')
          .expect(400);
      });
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for unknown routes', async () => {
      const response = await request(app)
        .get('/unknown-route')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('路由不存在'),
        code: 'NOT_FOUND',
      });
    });

    it('should handle malformed JSON', async () => {
      await request(app)
        .post('/v1/chat/completions')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting', async () => {
      // 发送多个请求以触发速率限制
      const requests = Array(10).fill(null).map(() =>
        request(app).get('/health')
      );

      const responses = await Promise.all(requests);
      
      // 所有请求都应该成功，因为健康检查端点使用宽松的速率限制
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status);
      });
    });
  });

  describe('CORS', () => {
    it('should include CORS headers', async () => {
      const response = await request(app)
        .options('/v1/models')
        .expect(204);

      expect(response.headers['access-control-allow-origin']).toBeDefined();
      expect(response.headers['access-control-allow-methods']).toBeDefined();
    });
  });

  describe('Security Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      // Helmet 添加的安全头
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBeDefined();
    });
  });
});
