import dotenv from 'dotenv';

// 加载测试环境变量
dotenv.config({ path: '.env.test' });

// 设置测试环境
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // 减少测试时的日志输出

// 全局测试配置
jest.setTimeout(10000);

// 模拟环境变量
process.env.OPENAI_API_KEY = 'test-openai-key';
process.env.GEMINI_API_KEY = 'test-gemini-key';
process.env.GOOGLE_CLOUD_PROJECT = 'test-project';
process.env.JWT_SECRET = 'test-jwt-secret';

// 全局测试钩子
beforeAll(async () => {
  // 测试前的全局设置
});

afterAll(async () => {
  // 测试后的全局清理
});

beforeEach(() => {
  // 每个测试前的设置
});

afterEach(() => {
  // 每个测试后的清理
});
