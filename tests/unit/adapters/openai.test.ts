import { OpenAIAdapter } from '@/adapters/openai';
import { AIProvider, ChatCompletionRequest } from '@/types';

// 模拟 OpenAI 客户端
jest.mock('openai', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    })),
  };
});

describe('OpenAIAdapter', () => {
  let adapter: OpenAIAdapter;
  let mockRequest: ChatCompletionRequest;

  beforeEach(() => {
    adapter = new OpenAIAdapter();
    mockRequest = {
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'user', content: 'Hello, world!' },
      ],
      temperature: 0.7,
      max_tokens: 100,
    };
  });

  describe('constructor', () => {
    it('should initialize with correct provider', () => {
      expect(adapter.provider).toBe(AIProvider.OPENAI);
    });
  });

  describe('validateRequest', () => {
    it('should validate a valid request', () => {
      expect(() => adapter.validateRequest(mockRequest)).not.toThrow();
    });

    it('should throw error for missing model', () => {
      const invalidRequest = { ...mockRequest, model: '' };
      expect(() => adapter.validateRequest(invalidRequest)).toThrow('模型参数不能为空');
    });

    it('should throw error for empty messages', () => {
      const invalidRequest = { ...mockRequest, messages: [] };
      expect(() => adapter.validateRequest(invalidRequest)).toThrow('消息数组不能为空');
    });

    it('should throw error for invalid temperature', () => {
      const invalidRequest = { ...mockRequest, temperature: 3 };
      expect(() => adapter.validateRequest(invalidRequest)).toThrow('温度参数必须在 0-2 之间');
    });
  });

  describe('transformRequest', () => {
    it('should transform request correctly', () => {
      const transformed = adapter.transformRequest(mockRequest);

      expect(transformed).toEqual({
        model: 'gpt-3.5-turbo',
        messages: [
          { role: 'user', content: 'Hello, world!' },
        ],
        temperature: 0.7,
        max_tokens: 100,
      });
    });

    it('should handle optional parameters', () => {
      const requestWithOptionals = {
        ...mockRequest,
        top_p: 0.9,
        frequency_penalty: 0.5,
        presence_penalty: 0.3,
        stop: ['\\n'],
      };

      const transformed = adapter.transformRequest(requestWithOptionals);

      expect(transformed).toMatchObject({
        top_p: 0.9,
        frequency_penalty: 0.5,
        presence_penalty: 0.3,
        stop: ['\\n'],
      });
    });
  });

  describe('transformResponse', () => {
    it('should transform OpenAI response correctly', () => {
      const openaiResponse = {
        id: 'chatcmpl-123',
        object: 'chat.completion' as const,
        created: 1677652288,
        model: 'gpt-3.5-turbo',
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant' as const,
              content: 'Hello! How can I help you today?',
              refusal: null,
            },
            finish_reason: 'stop' as const,
            logprobs: null,
          },
        ],
        usage: {
          prompt_tokens: 9,
          completion_tokens: 12,
          total_tokens: 21,
        },
      };

      const transformed = adapter.transformResponse(openaiResponse);

      expect(transformed).toEqual({
        id: 'chatcmpl-123',
        object: 'chat.completion',
        created: 1677652288,
        model: 'gpt-3.5-turbo',
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: 'Hello! How can I help you today?',
            },
            finish_reason: 'stop',
          },
        ],
        usage: {
          prompt_tokens: 9,
          completion_tokens: 12,
          total_tokens: 21,
        },
      });
    });
  });

  describe('getSupportedModels', () => {
    it('should return list of supported models', () => {
      const models = adapter.getSupportedModels();

      expect(Array.isArray(models)).toBe(true);
      expect(models.length).toBeGreaterThan(0);
      expect(models).toContain('gpt-3.5-turbo');
      expect(models).toContain('gpt-4');
    });
  });

  describe('isModelSupported', () => {
    it('should return true for supported models', () => {
      expect(adapter.isModelSupported('gpt-3.5-turbo')).toBe(true);
      expect(adapter.isModelSupported('gpt-4')).toBe(true);
    });

    it('should return false for unsupported models', () => {
      expect(adapter.isModelSupported('unsupported-model')).toBe(false);
    });
  });

  describe('getDefaultModel', () => {
    it('should return default model', () => {
      const defaultModel = (adapter as any).getDefaultModel();
      expect(typeof defaultModel).toBe('string');
      expect(defaultModel.length).toBeGreaterThan(0);
    });
  });
});
