import { Request, Response } from 'express';

// 基础类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  requestId: string;
}

// AI 服务提供商枚举
export enum AIProvider {
  OPENAI = 'openai',
  GEMINI = 'gemini'
}

// AI 模型提供商类型（用于动态配置）
export type AIModelProvider = 'openai' | 'gemini';

// 动态模型配置接口
export interface DynamicModelConfig {
  name: string;
  provider: AIModelProvider;
  apiKey: string;
  baseUrl: string;
  modelName: string;
  supportsVision?: boolean;
  isActive?: boolean;
  temperature?: number;
  maxTokens?: number;
  description?: string;
}

// 响应模式枚举
export enum ResponseMode {
  STREAM = 'stream',
  JSON = 'json'
}

// 认证模式枚举
export enum AuthMode {
  API_KEY = 'api-key',
  BEARER_TOKEN = 'bearer-token',
  OAUTH = 'oauth'
}

// 聊天消息接口
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  name?: string;
}

// 聊天完成请求接口
export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
  stream?: boolean;
  provider?: AIProvider;
  auth_mode?: AuthMode;
}

// 聊天完成响应接口
export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: ChatMessage;
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// 流式响应数据接口
export interface StreamChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: Partial<ChatMessage>;
    finish_reason?: string | null;
  }>;
}

// AI 适配器接口
export interface AIAdapter {
  provider: AIProvider;
  generateCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
  generateCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk>;
  validateRequest(request: ChatCompletionRequest): boolean;
  transformRequest(request: ChatCompletionRequest): any;
  transformResponse(response: any): ChatCompletionResponse;
}

// 配置接口
export interface ServerConfig {
  port: number;
  host: string;
  nodeEnv: string;
  corsOrigin: string;
  corsCredentials: boolean;
  rateLimitWindowMs: number;
  rateLimitMaxRequests: number;
  logLevel: string;
  logFile: string;
  proxyTimeout: number;
  proxyMaxRetries: number;
}

// OpenAI 配置接口
export interface OpenAIConfig {
  apiKey: string;
  baseUrl: string;
}

// Gemini 配置接口
export interface GeminiConfig {
  apiKey: string;
  baseUrl: string;
}

// Vertex AI 配置接口
export interface VertexAIConfig {
  projectId: string;
  location: string;
  credentialsPath?: string;
}

// 扩展的 Express Request 接口
export interface ExtendedRequest extends Request {
  requestId: string;
  startTime: number;
  user?: any;
  apiKey?: string;
}

// 扩展的 Express Response 接口
export interface ExtendedResponse extends Response {
  sendSuccess: (data?: any, message?: string) => void;
  sendError: (error: string, statusCode?: number) => void;
}

// 错误类型
export class ApiError extends Error {
  public statusCode: number;
  public code: string;
  public details?: any;

  constructor(message: string, statusCode: number = 500, code: string = 'INTERNAL_ERROR', details?: any) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
  }
}

// 日志级别枚举
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

// 健康检查状态
export interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    [key: string]: {
      status: 'up' | 'down';
      latency?: number;
      error?: string;
    };
  };
}
