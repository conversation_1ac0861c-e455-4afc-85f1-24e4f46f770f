import winston from 'winston';
// import { serverConfig } from '@/config'; // 避免循环依赖
import fs from 'fs';
import path from 'path';

// 确保日志目录存在
const logFile = process.env.LOG_FILE || 'logs/app.log';
const logDir = path.dirname(logFile);
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`;

    // 添加元数据
    if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta)}`;
    }

    return logMessage;
  })
);

// 控制台格式（开发环境）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss',
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} ${level}: ${message}`;

    // 在开发环境中显示更详细的元数据
    if (Object.keys(meta).length > 0 && process.env.NODE_ENV === 'development') {
      logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }

    return logMessage;
  })
);

// 创建传输器
const transports: winston.transport[] = [
  // 文件传输器 - 所有日志
  new winston.transports.File({
    filename: logFile,
    format: logFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    tailable: true,
  }),

  // 文件传输器 - 错误日志
  new winston.transports.File({
    filename: path.join(logDir, 'error.log'),
    level: 'error',
    format: logFormat,
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    tailable: true,
  }),
];

// 在非生产环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
    })
  );
}

// 创建 logger 实例
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports,
  // 处理未捕获的异常
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
      format: logFormat,
    }),
  ],
  // 处理未处理的 Promise 拒绝
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log'),
      format: logFormat,
    }),
  ],
});

// 性能日志记录器
export class PerformanceLogger {
  private static timers: Map<string, number> = new Map();

  /**
   * 开始计时
   */
  public static start(label: string): void {
    this.timers.set(label, Date.now());
  }

  /**
   * 结束计时并记录
   */
  public static end(label: string, metadata?: any): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      logger.warn(`性能计时器未找到: ${label}`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(label);

    logger.info(`性能指标: ${label}`, {
      duration: `${duration}ms`,
      ...metadata,
    });

    return duration;
  }

  /**
   * 记录内存使用情况
   */
  public static logMemoryUsage(label?: string): void {
    const memUsage = process.memoryUsage();
    const formatBytes = (bytes: number) => (bytes / 1024 / 1024).toFixed(2) + ' MB';

    logger.info(`内存使用情况${label ? ` [${label}]` : ''}`, {
      rss: formatBytes(memUsage.rss),
      heapTotal: formatBytes(memUsage.heapTotal),
      heapUsed: formatBytes(memUsage.heapUsed),
      external: formatBytes(memUsage.external),
    });
  }
}

// 请求日志记录器
export class RequestLogger {
  /**
   * 记录请求开始
   */
  public static logStart(requestId: string, method: string, url: string, metadata?: any): void {
    logger.info('请求开始', {
      requestId,
      method,
      url,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录请求结束
   */
  public static logEnd(
    requestId: string,
    statusCode: number,
    duration: number,
    metadata?: any
  ): void {
    const level = statusCode >= 400 ? 'error' : 'info';

    logger.log(level, '请求结束', {
      requestId,
      statusCode,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录请求错误
   */
  public static logError(requestId: string, error: Error, metadata?: any): void {
    logger.error('请求错误', {
      requestId,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }
}

// API 调用日志记录器
export class APILogger {
  /**
   * 记录 API 调用开始
   */
  public static logAPICall(
    provider: string,
    endpoint: string,
    requestId: string,
    metadata?: any
  ): void {
    logger.info('API 调用开始', {
      provider,
      endpoint,
      requestId,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录 API 调用成功
   */
  public static logAPISuccess(
    provider: string,
    endpoint: string,
    requestId: string,
    duration: number,
    metadata?: any
  ): void {
    logger.info('API 调用成功', {
      provider,
      endpoint,
      requestId,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录 API 调用失败
   */
  public static logAPIError(
    provider: string,
    endpoint: string,
    requestId: string,
    error: Error,
    metadata?: any
  ): void {
    logger.error('API 调用失败', {
      provider,
      endpoint,
      requestId,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      timestamp: new Date().toISOString(),
      ...metadata,
    });
  }
}

// 导出默认 logger
export default logger;
