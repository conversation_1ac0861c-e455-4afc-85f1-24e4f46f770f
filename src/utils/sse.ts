import { Response } from 'express';
import { ExtendedRequest, StreamChunk } from '@/types';
import { logger } from './logger';

/**
 * SSE 连接管理器
 * 管理所有活跃的 SSE 连接
 */
export class SSEConnectionManager {
  private static instance: SSEConnectionManager;
  private connections: Map<string, SSEConnection> = new Map();

  private constructor() {}

  public static getInstance(): SSEConnectionManager {
    if (!this.instance) {
      this.instance = new SSEConnectionManager();
    }
    return this.instance;
  }

  /**
   * 添加连接
   */
  public addConnection(connectionId: string, connection: SSEConnection): void {
    this.connections.set(connectionId, connection);
    logger.info(`SSE 连接已建立: ${connectionId}`);
  }

  /**
   * 移除连接
   */
  public removeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.close();
      this.connections.delete(connectionId);
      logger.info(`SSE 连接已关闭: ${connectionId}`);
    }
  }

  /**
   * 获取连接
   */
  public getConnection(connectionId: string): SSEConnection | undefined {
    return this.connections.get(connectionId);
  }

  /**
   * 获取所有活跃连接数
   */
  public getActiveConnectionCount(): number {
    return this.connections.size;
  }

  /**
   * 广播消息到所有连接
   */
  public broadcast(data: any, event?: string): void {
    for (const connection of this.connections.values()) {
      if (connection.isActive()) {
        connection.send(data, event);
      }
    }
  }

  /**
   * 清理所有连接
   */
  public cleanup(): void {
    for (const [connectionId, connection] of this.connections) {
      connection.close();
      this.connections.delete(connectionId);
    }
    logger.info('所有 SSE 连接已清理');
  }
}

/**
 * SSE 连接类
 * 封装单个 SSE 连接的操作
 */
export class SSEConnection {
  private response: Response;
  private requestId: string;
  private isConnected: boolean = false;
  private heartbeatInterval?: NodeJS.Timeout;
  private lastActivity: number = Date.now();

  constructor(req: ExtendedRequest, res: Response) {
    this.response = res;
    this.requestId = req.requestId;
    this.setupConnection();
    this.setupHeartbeat();
    this.setupCleanup();
  }

  /**
   * 设置 SSE 连接
   */
  private setupConnection(): void {
    // 设置 SSE 响应头
    this.response.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
      'X-Accel-Buffering': 'no', // 禁用 Nginx 缓冲
    });

    this.isConnected = true;

    // 发送初始连接消息
    this.send({ type: 'connection', status: 'connected', requestId: this.requestId }, 'connect');

    logger.info(`SSE 连接已建立: ${this.requestId}`);
  }

  /**
   * 设置心跳机制
   */
  private setupHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.ping();
      }
    }, 30000); // 每30秒发送一次心跳
  }

  /**
   * 设置清理机制
   */
  private setupCleanup(): void {
    // 监听客户端断开连接
    this.response.on('close', () => {
      this.close();
    });

    this.response.on('error', (error) => {
      logger.error(`SSE 连接错误 [${this.requestId}]:`, error);
      this.close();
    });

    // 监听进程退出
    process.on('SIGINT', () => {
      this.close();
    });

    process.on('SIGTERM', () => {
      this.close();
    });
  }

  /**
   * 发送数据
   */
  public send(data: any, event?: string, id?: string): boolean {
    if (!this.isConnected) {
      return false;
    }

    try {
      let message = '';

      // 添加事件 ID
      if (id) {
        message += `id: ${id}\n`;
      }

      // 添加事件类型
      if (event) {
        message += `event: ${event}\n`;
      }

      // 添加数据
      const jsonData = typeof data === 'string' ? data : JSON.stringify(data);
      const lines = jsonData.split('\n');
      for (const line of lines) {
        message += `data: ${line}\n`;
      }

      message += '\n';

      this.response.write(message);
      this.lastActivity = Date.now();

      return true;
    } catch (error) {
      logger.error(`发送 SSE 数据失败 [${this.requestId}]:`, error);
      this.close();
      return false;
    }
  }

  /**
   * 发送心跳
   */
  public ping(): boolean {
    return this.send({ type: 'ping', timestamp: Date.now() }, 'ping');
  }

  /**
   * 发送流式数据块
   */
  public sendChunk(chunk: StreamChunk): boolean {
    return this.send(chunk, 'chunk', chunk.id);
  }

  /**
   * 发送完成信号
   */
  public sendComplete(data?: any): boolean {
    const result = this.send({ type: 'complete', data }, 'complete');
    this.close();
    return result;
  }

  /**
   * 发送错误信息
   */
  public sendError(error: string, code?: string): boolean {
    const result = this.send({ type: 'error', error, code }, 'error');
    this.close();
    return result;
  }

  /**
   * 检查连接是否活跃
   */
  public isActive(): boolean {
    return this.isConnected && !this.response.destroyed;
  }

  /**
   * 获取连接信息
   */
  public getInfo(): any {
    return {
      requestId: this.requestId,
      isConnected: this.isConnected,
      lastActivity: this.lastActivity,
      uptime: Date.now() - this.lastActivity,
    };
  }

  /**
   * 关闭连接
   */
  public close(): void {
    if (this.isConnected) {
      this.isConnected = false;

      // 清理心跳定时器
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = undefined;
      }

      // 发送关闭消息
      try {
        this.response.write('event: close\ndata: {"type":"close"}\n\n');
        this.response.end();
      } catch (error) {
        // 忽略关闭时的错误
      }

      logger.info(`SSE 连接已关闭: ${this.requestId}`);
    }
  }
}

/**
 * SSE 工具函数
 */
export class SSEUtils {
  /**
   * 创建 SSE 连接
   */
  public static createConnection(req: ExtendedRequest, res: Response): SSEConnection {
    const connection = new SSEConnection(req, res);
    const manager = SSEConnectionManager.getInstance();
    manager.addConnection(req.requestId, connection);
    return connection;
  }

  /**
   * 格式化 SSE 消息
   */
  public static formatMessage(data: any, event?: string, id?: string): string {
    let message = '';

    if (id) {
      message += `id: ${id}\n`;
    }

    if (event) {
      message += `event: ${event}\n`;
    }

    const jsonData = typeof data === 'string' ? data : JSON.stringify(data);
    const lines = jsonData.split('\n');
    for (const line of lines) {
      message += `data: ${line}\n`;
    }

    message += '\n';
    return message;
  }

  /**
   * 验证 SSE 支持
   */
  public static isSSESupported(req: ExtendedRequest): boolean {
    const accept = req.headers.accept || '';
    return accept.includes('text/event-stream') || accept.includes('*/*');
  }

  /**
   * 获取连接统计信息
   */
  public static getStats(): any {
    const manager = SSEConnectionManager.getInstance();
    return {
      activeConnections: manager.getActiveConnectionCount(),
      timestamp: new Date().toISOString(),
    };
  }
}

// 导出单例管理器
export const sseManager = SSEConnectionManager.getInstance();
