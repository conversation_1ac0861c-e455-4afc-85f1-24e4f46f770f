import dotenv from 'dotenv';
import Jo<PERSON> from 'joi';
import { ServerConfig, OpenAIConfig, GeminiConfig, VertexAIConfig } from '@/types';

// 加载环境变量
dotenv.config();

// 环境变量验证模式
const envSchema = Joi.object({
  // 服务器配置
  PORT: Joi.number().default(3000),
  HOST: Joi.string().default('localhost'),
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  
  // CORS 配置
  CORS_ORIGIN: Joi.string().default('*'),
  CORS_CREDENTIALS: Joi.boolean().default(false),
  
  // 速率限制配置
  RATE_LIMIT_WINDOW_MS: Joi.number().default(900000), // 15分钟
  RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),
  
  // 日志配置
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),
  LOG_FILE: Joi.string().default('logs/app.log'),
  
  // 代理配置
  PROXY_TIMEOUT: Joi.number().default(30000), // 30秒
  PROXY_MAX_RETRIES: Joi.number().default(3),
  
  // OpenAI 配置
  OPENAI_API_KEY: Joi.string().required(),
  OPENAI_BASE_URL: Joi.string().default('https://api.openai.com/v1'),
  
  // Gemini 配置
  GEMINI_API_KEY: Joi.string().required(),
  GEMINI_BASE_URL: Joi.string().default('https://generativelanguage.googleapis.com'),
  
  // Vertex AI 配置
  GOOGLE_CLOUD_PROJECT: Joi.string().required(),
  GOOGLE_CLOUD_LOCATION: Joi.string().default('us-central1'),
  GOOGLE_APPLICATION_CREDENTIALS: Joi.string().optional(),
  
  // 认证配置
  JWT_SECRET: Joi.string().required(),
  API_KEY_HEADER: Joi.string().default('X-API-Key'),
}).unknown();

// 验证环境变量
const { error, value: envVars } = envSchema.validate(process.env);

if (error) {
  throw new Error(`配置验证错误: ${error.message}`);
}

// 服务器配置
export const serverConfig: ServerConfig = {
  port: envVars.PORT,
  host: envVars.HOST,
  nodeEnv: envVars.NODE_ENV,
  corsOrigin: envVars.CORS_ORIGIN,
  corsCredentials: envVars.CORS_CREDENTIALS,
  rateLimitWindowMs: envVars.RATE_LIMIT_WINDOW_MS,
  rateLimitMaxRequests: envVars.RATE_LIMIT_MAX_REQUESTS,
  logLevel: envVars.LOG_LEVEL,
  logFile: envVars.LOG_FILE,
  proxyTimeout: envVars.PROXY_TIMEOUT,
  proxyMaxRetries: envVars.PROXY_MAX_RETRIES,
};

// OpenAI 配置
export const openaiConfig: OpenAIConfig = {
  apiKey: envVars.OPENAI_API_KEY,
  baseUrl: envVars.OPENAI_BASE_URL,
};

// Gemini 配置
export const geminiConfig: GeminiConfig = {
  apiKey: envVars.GEMINI_API_KEY,
  baseUrl: envVars.GEMINI_BASE_URL,
};

// Vertex AI 配置
export const vertexAIConfig: VertexAIConfig = {
  projectId: envVars.GOOGLE_CLOUD_PROJECT,
  location: envVars.GOOGLE_CLOUD_LOCATION,
  credentialsPath: envVars.GOOGLE_APPLICATION_CREDENTIALS,
};

// 认证配置
export const authConfig = {
  jwtSecret: envVars.JWT_SECRET,
  apiKeyHeader: envVars.API_KEY_HEADER,
};

// 导出所有配置
export const config = {
  server: serverConfig,
  openai: openaiConfig,
  gemini: geminiConfig,
  vertexAI: vertexAIConfig,
  auth: authConfig,
};

// 配置验证函数
export function validateConfig(): void {
  const requiredConfigs = [
    { name: 'OpenAI API Key', value: openaiConfig.apiKey },
    { name: 'Gemini API Key', value: geminiConfig.apiKey },
    { name: 'Google Cloud Project', value: vertexAIConfig.projectId },
    { name: 'JWT Secret', value: authConfig.jwtSecret },
  ];

  const missingConfigs = requiredConfigs.filter(config => !config.value);
  
  if (missingConfigs.length > 0) {
    const missing = missingConfigs.map(config => config.name).join(', ');
    throw new Error(`缺少必需的配置: ${missing}`);
  }
}

// 获取环境特定配置
export function getEnvironmentConfig() {
  return {
    isDevelopment: serverConfig.nodeEnv === 'development',
    isProduction: serverConfig.nodeEnv === 'production',
    isTest: serverConfig.nodeEnv === 'test',
  };
}

export default config;
export { modelConfigManager } from './models';
