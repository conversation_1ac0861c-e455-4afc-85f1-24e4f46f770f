import { DynamicModelConfig, AIModelProvider } from '@/types';
import { logger } from '@/utils/logger';

/**
 * 动态模型配置管理器
 * 支持根据模型名称动态选择配置
 */
export class ModelConfigManager {
  private static instance: ModelConfigManager;
  private modelConfigs: Map<string, DynamicModelConfig> = new Map();

  private constructor() {
    this.initializeDefaultModels();
  }

  public static getInstance(): ModelConfigManager {
    if (!this.instance) {
      this.instance = new ModelConfigManager();
    }
    return this.instance;
  }

  /**
   * 初始化默认模型配置
   */
  private initializeDefaultModels(): void {
    const defaultModels: DynamicModelConfig[] = [
      {
        name: '夏目官方模型1',
        provider: 'openai' as AIModelProvider,
        apiKey: 'sk-jipjycienusxsfdptoweqvagdillzrumjjtcblfjfsrdhqxk',
        baseUrl: 'https://api.siliconflow.cn',
        modelName: 'Qwen/Qwen2.5-VL-72B-Instruct',
        supportsVision: true,
        isActive: true,
        description: '通义千问视觉大模型，支持图像理解'
      },
      {
        name: '夏目官方模型2',
        provider: 'openai' as AIModelProvider,
        apiKey: 'sk-YnVBTQs0OLXNk2aQFElOtFPOEqQLe0kaQEbivMvkuFFKqYpn',
        baseUrl: 'https://tbai.xin',
        modelName: 'gemini-2.5-pro',
        supportsVision: true,
        isActive: true,
        description: 'Gemini 2.5 Pro 模型，支持多模态'
      },
      {
        name: '需科学上网',
        provider: 'openai' as AIModelProvider,
        apiKey: 'sk-fCr04jsVUMdECSXyfSBYreh6da0ePzTwVmdctxSIQnGgfSxh',
        baseUrl: 'https://x666.me',
        modelName: 'gemini-2.5-pro',
        supportsVision: true,
        isActive: true,
        description: 'Gemini 2.5 Pro 模型（需要科学上网）'
      },
    ];

    // 注册所有默认模型
    defaultModels.forEach(config => {
      this.registerModel(config);
    });

    logger.info('动态模型配置初始化完成', {
      totalModels: this.modelConfigs.size,
      activeModels: this.getActiveModels().length
    });
  }

  /**
   * 注册新的模型配置
   */
  public registerModel(config: DynamicModelConfig): void {
    this.modelConfigs.set(config.name, config);
    logger.debug('注册模型配置', { name: config.name, provider: config.provider });
  }

  /**
   * 根据模型名称获取配置
   */
  public getModelConfig(modelName: string): DynamicModelConfig | null {
    const config = this.modelConfigs.get(modelName);
    if (!config) {
      logger.warn('未找到模型配置', { modelName });
      return null;
    }

    if (!config.isActive) {
      logger.warn('模型配置未激活', { modelName });
      return null;
    }

    return config;
  }

  /**
   * 获取所有可用的模型配置
   */
  public getAllModels(): DynamicModelConfig[] {
    return Array.from(this.modelConfigs.values());
  }

  /**
   * 获取所有激活的模型配置
   */
  public getActiveModels(): DynamicModelConfig[] {
    return Array.from(this.modelConfigs.values()).filter(config => config.isActive);
  }

  /**
   * 根据提供商获取模型配置
   */
  public getModelsByProvider(provider: AIModelProvider): DynamicModelConfig[] {
    return Array.from(this.modelConfigs.values()).filter(
      config => config.provider === provider && config.isActive
    );
  }

  /**
   * 更新模型配置
   */
  public updateModel(modelName: string, updates: Partial<DynamicModelConfig>): boolean {
    const config = this.modelConfigs.get(modelName);
    if (!config) {
      logger.warn('尝试更新不存在的模型配置', { modelName });
      return false;
    }

    const updatedConfig = { ...config, ...updates };
    this.modelConfigs.set(modelName, updatedConfig);
    
    logger.info('模型配置已更新', { modelName, updates });
    return true;
  }

  /**
   * 删除模型配置
   */
  public removeModel(modelName: string): boolean {
    const deleted = this.modelConfigs.delete(modelName);
    if (deleted) {
      logger.info('模型配置已删除', { modelName });
    } else {
      logger.warn('尝试删除不存在的模型配置', { modelName });
    }
    return deleted;
  }

  /**
   * 激活/停用模型
   */
  public setModelActive(modelName: string, isActive: boolean): boolean {
    return this.updateModel(modelName, { isActive });
  }

  /**
   * 检查模型是否存在且激活
   */
  public isModelAvailable(modelName: string): boolean {
    const config = this.modelConfigs.get(modelName);
    return config ? config.isActive === true : false;
  }

  /**
   * 获取模型统计信息
   */
  public getModelStats(): {
    total: number;
    active: number;
    byProvider: Record<string, number>;
  } {
    const allModels = this.getAllModels();
    const activeModels = this.getActiveModels();
    
    const byProvider: Record<string, number> = {};
    activeModels.forEach(model => {
      byProvider[model.provider] = (byProvider[model.provider] || 0) + 1;
    });

    return {
      total: allModels.length,
      active: activeModels.length,
      byProvider
    };
  }

  /**
   * 验证模型配置
   */
  public validateModelConfig(config: DynamicModelConfig): string[] {
    const errors: string[] = [];

    if (!config.name || config.name.trim() === '') {
      errors.push('模型名称不能为空');
    }

    if (!config.provider) {
      errors.push('提供商不能为空');
    }

    if (!config.apiKey || config.apiKey.trim() === '') {
      errors.push('API 密钥不能为空');
    }

    if (!config.baseUrl || config.baseUrl.trim() === '') {
      errors.push('基础 URL 不能为空');
    }

    if (!config.modelName || config.modelName.trim() === '') {
      errors.push('模型名称不能为空');
    }

    // 验证 URL 格式
    try {
      new URL(config.baseUrl);
    } catch {
      errors.push('基础 URL 格式无效');
    }

    return errors;
  }

  /**
   * 从环境变量或配置文件加载模型配置
   */
  public loadModelsFromConfig(configs: DynamicModelConfig[]): void {
    configs.forEach(config => {
      const errors = this.validateModelConfig(config);
      if (errors.length === 0) {
        this.registerModel(config);
      } else {
        logger.error('模型配置验证失败', { name: config.name, errors });
      }
    });
  }

  /**
   * 导出模型配置（用于备份或迁移）
   */
  public exportConfigs(): DynamicModelConfig[] {
    return this.getAllModels();
  }

  /**
   * 清空所有模型配置
   */
  public clearAllModels(): void {
    this.modelConfigs.clear();
    logger.info('所有模型配置已清空');
  }

  /**
   * 重置为默认配置
   */
  public resetToDefaults(): void {
    this.clearAllModels();
    this.initializeDefaultModels();
    logger.info('模型配置已重置为默认值');
  }
}

// 导出单例实例
export const modelConfigManager = ModelConfigManager.getInstance();
