import { 
  AIAdapter, 
  AIProvider, 
  ChatCompletionRequest, 
  ChatCompletionResponse, 
  StreamChunk,
  ApiError 
} from '@/types';
import { logger } from '@/utils/logger';

/**
 * AI 适配器基类
 * 提供通用的错误处理、日志记录和验证功能
 */
export abstract class BaseAdapter implements AIAdapter {
  public abstract readonly provider: AIProvider;
  protected abstract readonly baseUrl: string;
  protected abstract readonly apiKey: string;

  /**
   * 生成聊天完成响应
   */
  public abstract generateCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;

  /**
   * 生成流式聊天完成响应
   */
  public abstract generateCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk>;

  /**
   * 验证请求参数
   */
  public validateRequest(request: ChatCompletionRequest): boolean {
    try {
      // 基础验证
      if (!request.model) {
        throw new ApiError('模型参数不能为空', 400, 'MISSING_MODEL');
      }

      if (!request.messages || !Array.isArray(request.messages) || request.messages.length === 0) {
        throw new ApiError('消息数组不能为空', 400, 'MISSING_MESSAGES');
      }

      // 验证消息格式
      for (const message of request.messages) {
        if (!message.role || !['system', 'user', 'assistant'].includes(message.role)) {
          throw new ApiError('无效的消息角色', 400, 'INVALID_MESSAGE_ROLE');
        }
        if (!message.content || typeof message.content !== 'string') {
          throw new ApiError('消息内容不能为空', 400, 'MISSING_MESSAGE_CONTENT');
        }
      }

      // 验证可选参数
      if (request.temperature !== undefined && (request.temperature < 0 || request.temperature > 2)) {
        throw new ApiError('温度参数必须在 0-2 之间', 400, 'INVALID_TEMPERATURE');
      }

      if (request.max_tokens !== undefined && request.max_tokens <= 0) {
        throw new ApiError('最大令牌数必须大于 0', 400, 'INVALID_MAX_TOKENS');
      }

      if (request.top_p !== undefined && (request.top_p < 0 || request.top_p > 1)) {
        throw new ApiError('top_p 参数必须在 0-1 之间', 400, 'INVALID_TOP_P');
      }

      return true;
    } catch (error) {
      logger.error(`请求验证失败 [${this.provider}]:`, error);
      throw error;
    }
  }

  /**
   * 转换请求格式（子类实现具体转换逻辑）
   */
  public abstract transformRequest(request: ChatCompletionRequest): any;

  /**
   * 转换响应格式（子类实现具体转换逻辑）
   */
  public abstract transformResponse(response: any): ChatCompletionResponse;

  /**
   * 处理 API 错误
   */
  protected handleApiError(error: any, context: string): never {
    logger.error(`API 错误 [${this.provider}] ${context}:`, error);

    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      switch (status) {
        case 400:
          throw new ApiError(`请求参数错误: ${data?.error?.message || '未知错误'}`, 400, 'BAD_REQUEST', data);
        case 401:
          throw new ApiError('API 密钥无效或已过期', 401, 'UNAUTHORIZED');
        case 403:
          throw new ApiError('访问被拒绝，请检查权限', 403, 'FORBIDDEN');
        case 404:
          throw new ApiError('请求的资源不存在', 404, 'NOT_FOUND');
        case 429:
          throw new ApiError('请求频率过高，请稍后重试', 429, 'RATE_LIMITED');
        case 500:
          throw new ApiError('服务器内部错误', 500, 'INTERNAL_SERVER_ERROR');
        case 502:
          throw new ApiError('网关错误', 502, 'BAD_GATEWAY');
        case 503:
          throw new ApiError('服务暂时不可用', 503, 'SERVICE_UNAVAILABLE');
        default:
          throw new ApiError(`API 请求失败: ${data?.error?.message || '未知错误'}`, status, 'API_ERROR', data);
      }
    } else if (error.code === 'ECONNREFUSED') {
      throw new ApiError('无法连接到 API 服务器', 503, 'CONNECTION_REFUSED');
    } else if (error.code === 'ETIMEDOUT') {
      throw new ApiError('请求超时', 408, 'TIMEOUT');
    } else {
      throw new ApiError(`网络错误: ${error.message}`, 500, 'NETWORK_ERROR');
    }
  }

  /**
   * 生成唯一请求 ID
   */
  protected generateRequestId(): string {
    return `${this.provider}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 记录请求日志
   */
  protected logRequest(request: ChatCompletionRequest, requestId: string): void {
    logger.info(`[${this.provider}] 请求开始`, {
      requestId,
      model: request.model,
      messageCount: request.messages.length,
      stream: request.stream || false,
      temperature: request.temperature,
      maxTokens: request.max_tokens
    });
  }

  /**
   * 记录响应日志
   */
  protected logResponse(response: ChatCompletionResponse, requestId: string, duration: number): void {
    logger.info(`[${this.provider}] 请求完成`, {
      requestId,
      duration: `${duration}ms`,
      model: response.model,
      usage: response.usage,
      finishReason: response.choices[0]?.finish_reason
    });
  }

  /**
   * 获取默认请求头
   */
  protected getDefaultHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'User-Agent': 'AI-Proxy-Server/1.0.0',
    };
  }

  /**
   * 检查服务健康状态
   */
  public async healthCheck(): Promise<{ status: 'up' | 'down'; latency?: number; error?: string }> {
    try {
      const startTime = Date.now();
      
      // 发送简单的健康检查请求
      const testRequest: ChatCompletionRequest = {
        model: this.getDefaultModel(),
        messages: [{ role: 'user', content: 'ping' }],
        max_tokens: 1,
        temperature: 0
      };

      await this.generateCompletion(testRequest);
      
      const latency = Date.now() - startTime;
      return { status: 'up', latency };
    } catch (error) {
      logger.error(`[${this.provider}] 健康检查失败:`, error);
      return { 
        status: 'down', 
        error: error instanceof Error ? error.message : '未知错误' 
      };
    }
  }

  /**
   * 获取默认模型（子类实现）
   */
  protected abstract getDefaultModel(): string;
}
