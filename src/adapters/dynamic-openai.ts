import {
  AIProvider,
  ChatCompletionRequest,
  ChatCompletionResponse,
  DynamicModelConfig,
  StreamChunk
} from '@/types';
import { logger } from '@/utils/logger';
import OpenAI from 'openai';
import { BaseAdapter } from './base';

/**
 * 动态 OpenAI 适配器
 * 支持根据动态配置创建不同的 OpenAI 兼容客户端
 */
export class DynamicOpenAIAdapter extends BaseAdapter {
  public readonly provider = AIProvider.OPENAI;
  protected readonly baseUrl: string;
  protected readonly apiKey: string;

  private client: OpenAI;
  private modelConfig: DynamicModelConfig;

  constructor(modelConfig: DynamicModelConfig) {
    super();
    this.modelConfig = modelConfig;
    this.baseUrl = modelConfig.baseUrl;
    this.apiKey = modelConfig.apiKey;

    // 创建 OpenAI 客户端实例
    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseUrl,
      timeout: 30000, // 30秒超时
      maxRetries: 3,
    });

    logger.info('动态 OpenAI 适配器已创建', {
      modelName: modelConfig.name,
      provider: modelConfig.provider,
      baseUrl: modelConfig.baseUrl,
      actualModel: modelConfig.modelName
    });
  }

  /**
   * 生成聊天完成响应
   */
  public async generateCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.validateRequest(request);
      this.logRequest(request, requestId);

      // 使用配置中的实际模型名称
      const openaiRequest = this.transformRequest(request);
      openaiRequest.model = this.modelConfig.modelName;

      const response = await this.client.chat.completions.create({
        ...openaiRequest,
        stream: false,
      });

      const transformedResponse = this.transformResponse(response);
      const duration = Date.now() - startTime;

      this.logResponse(transformedResponse, requestId, duration);

      return transformedResponse;
    } catch (error) {
      this.handleApiError(error, `generateCompletion [${requestId}]`);
    }
  }

  /**
   * 生成流式聊天完成响应
   */
  public async* generateCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.validateRequest(request);
      this.logRequest(request, requestId);

      // 使用配置中的实际模型名称
      const openaiRequest = this.transformRequest(request);
      openaiRequest.model = this.modelConfig.modelName;

      const stream = await this.client.chat.completions.create({
        ...openaiRequest,
        stream: true,
      });

      let chunkCount = 0;

      for await (const chunk of stream) {
        chunkCount++;

        const transformedChunk: StreamChunk = {
          id: chunk.id,
          object: chunk.object,
          created: chunk.created,
          model: this.modelConfig.name, // 返回用户友好的模型名称
          choices: chunk.choices.map(choice => ({
            index: choice.index,
            delta: {
              role: choice.delta.role as 'system' | 'user' | 'assistant' | undefined,
              content: choice.delta.content || '',
            },
            finish_reason: choice.finish_reason || null,
          })),
        };

        yield transformedChunk;
      }

      const duration = Date.now() - startTime;
      logger.info(`[${this.provider}] 流式请求完成`, {
        requestId,
        duration: `${duration}ms`,
        chunkCount,
        model: this.modelConfig.name,
        actualModel: this.modelConfig.modelName,
      });

    } catch (error) {
      this.handleApiError(error, `generateCompletionStream [${requestId}]`);
    }
  }

  /**
   * 转换请求格式为 OpenAI 格式
   */
  public transformRequest(request: ChatCompletionRequest): OpenAI.Chat.Completions.ChatCompletionCreateParams {
    const openaiRequest: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
      model: request.model, // 这里会被后续替换为实际模型名称
      messages: request.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        ...(msg.name && { name: msg.name }),
      })),
    };

    // 添加可选参数，优先使用模型配置中的默认值
    if (request.temperature !== undefined) {
      openaiRequest.temperature = request.temperature;
    } else if (this.modelConfig.temperature !== undefined) {
      openaiRequest.temperature = this.modelConfig.temperature;
    }

    if (request.max_tokens !== undefined) {
      openaiRequest.max_tokens = request.max_tokens;
    } else if (this.modelConfig.maxTokens !== undefined) {
      openaiRequest.max_tokens = this.modelConfig.maxTokens;
    }

    if (request.top_p !== undefined) {
      openaiRequest.top_p = request.top_p;
    }
    if (request.frequency_penalty !== undefined) {
      openaiRequest.frequency_penalty = request.frequency_penalty;
    }
    if (request.presence_penalty !== undefined) {
      openaiRequest.presence_penalty = request.presence_penalty;
    }
    if (request.stop !== undefined) {
      openaiRequest.stop = request.stop;
    }

    return openaiRequest;
  }

  /**
   * 转换响应格式为统一格式
   */
  public transformResponse(response: OpenAI.Chat.Completions.ChatCompletion): ChatCompletionResponse {
    return {
      id: response.id,
      object: response.object,
      created: response.created,
      model: this.modelConfig.name, // 返回用户友好的模型名称
      choices: response.choices.map(choice => ({
        index: choice.index,
        message: {
          role: choice.message.role,
          content: choice.message.content || '',
          ...((choice.message as any).name && { name: (choice.message as any).name }),
        },
        finish_reason: choice.finish_reason || 'stop',
      })),
      usage: {
        prompt_tokens: response.usage?.prompt_tokens || 0,
        completion_tokens: response.usage?.completion_tokens || 0,
        total_tokens: response.usage?.total_tokens || 0,
      },
    };
  }

  /**
   * 获取默认模型
   */
  protected getDefaultModel(): string {
    return this.modelConfig.name;
  }

  /**
   * 获取实际模型名称
   */
  public getActualModelName(): string {
    return this.modelConfig.modelName;
  }

  /**
   * 获取模型配置
   */
  public getModelConfig(): DynamicModelConfig {
    return { ...this.modelConfig };
  }

  /**
   * 检查是否支持视觉功能
   */
  public supportsVision(): boolean {
    return this.modelConfig.supportsVision || false;
  }

  /**
   * 获取支持的模型列表（返回配置的模型名称）
   */
  public getSupportedModels(): string[] {
    return [this.modelConfig.name];
  }

  /**
   * 验证模型是否支持
   */
  public isModelSupported(model: string): boolean {
    return model === this.modelConfig.name;
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<{ status: 'up' | 'down'; latency?: number; error?: string }> {
    try {
      const startTime = Date.now();

      // 发送简单的健康检查请求
      const testRequest: ChatCompletionRequest = {
        model: this.modelConfig.name,
        messages: [{ role: 'user', content: 'ping' }],
        max_tokens: 1,
        temperature: 0
      };

      await this.generateCompletion(testRequest);

      const latency = Date.now() - startTime;
      return { status: 'up', latency };
    } catch (error) {
      logger.error(`[${this.modelConfig.name}] 健康检查失败:`, error);
      return {
        status: 'down',
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 获取适配器信息
   */
  public getAdapterInfo(): any {
    return {
      provider: this.provider,
      modelName: this.modelConfig.name,
      actualModel: this.modelConfig.modelName,
      baseUrl: this.modelConfig.baseUrl,
      supportsVision: this.modelConfig.supportsVision,
      isActive: this.modelConfig.isActive,
      description: this.modelConfig.description,
    };
  }
}
