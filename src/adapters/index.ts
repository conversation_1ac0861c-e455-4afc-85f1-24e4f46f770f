import { modelConfigManager } from '@/config/models';
import { AIAdapter, AIProvider, ApiError, DynamicModelConfig } from '@/types';
import { logger } from '@/utils/logger';
import { DynamicOpenAIAdapter } from './dynamic-openai';
import { GeminiAdapter } from './gemini';
import { OpenAIAdapter } from './openai';

/**
 * AI 适配器工厂类
 * 负责创建和管理不同的 AI 服务提供商适配器
 * 支持动态模型配置
 */
export class AdapterFactory {
  private static adapters: Map<AIProvider, AIAdapter> = new Map();
  private static dynamicAdapters: Map<string, DynamicOpenAIAdapter> = new Map();

  /**
   * 根据模型名称获取动态适配器
   */
  public static getDynamicAdapter(modelName: string): AIAdapter {
    // 首先检查是否已有缓存的动态适配器
    if (this.dynamicAdapters.has(modelName)) {
      const adapter = this.dynamicAdapters.get(modelName);
      if (adapter) {
        return adapter;
      }
    }

    // 从模型配置管理器获取配置
    const modelConfig = modelConfigManager.getModelConfig(modelName);
    if (!modelConfig) {
      throw new ApiError(`未找到模型配置: ${modelName}`, 400, 'MODEL_NOT_FOUND');
    }

    // 创建动态适配器
    const adapter = this.createDynamicAdapter(modelConfig);
    this.dynamicAdapters.set(modelName, adapter);

    logger.info('创建动态适配器', {
      modelName,
      provider: modelConfig.provider,
      actualModel: modelConfig.modelName
    });

    return adapter;
  }

  /**
   * 获取适配器实例（传统方式）
   */
  public static getAdapter(provider: AIProvider): AIAdapter {
    if (!this.adapters.has(provider)) {
      this.adapters.set(provider, this.createAdapter(provider));
    }

    const adapter = this.adapters.get(provider);
    if (!adapter) {
      throw new ApiError(`不支持的 AI 提供商: ${provider}`, 400, 'UNSUPPORTED_PROVIDER');
    }

    return adapter;
  }

  /**
   * 创建动态适配器实例
   */
  private static createDynamicAdapter(modelConfig: DynamicModelConfig): DynamicOpenAIAdapter {
    // 目前所有动态模型都使用 OpenAI 兼容的接口
    return new DynamicOpenAIAdapter(modelConfig);
  }

  /**
   * 创建适配器实例（传统方式）
   */
  private static createAdapter(provider: AIProvider): AIAdapter {
    switch (provider) {
      case AIProvider.OPENAI:
        return new OpenAIAdapter();
      case AIProvider.GEMINI:
        return new GeminiAdapter();
      default:
        throw new ApiError(`不支持的 AI 提供商: ${provider}`, 400, 'UNSUPPORTED_PROVIDER');
    }
  }

  /**
   * 获取所有支持的提供商
   */
  public static getSupportedProviders(): AIProvider[] {
    return Object.values(AIProvider);
  }

  /**
   * 检查提供商是否支持
   */
  public static isProviderSupported(provider: string): boolean {
    return Object.values(AIProvider).includes(provider as AIProvider);
  }

  /**
   * 根据模型名称自动检测提供商
   */
  public static detectProvider(model: string): AIProvider {
    // 首先检查是否是动态配置的模型
    const modelConfig = modelConfigManager.getModelConfig(model);
    if (modelConfig) {
      // 将动态配置的提供商映射到 AIProvider 枚举
      switch (modelConfig.provider) {
        case 'openai':
          return AIProvider.OPENAI;
        case 'gemini':
          return AIProvider.GEMINI;
        default:
          return AIProvider.OPENAI;
      }
    }

    // 传统的模型名称检测
    if (model.startsWith('gpt-') || model.includes('davinci') || model.includes('curie')) {
      return AIProvider.OPENAI;
    }

    if (model.startsWith('gemini-') || model.includes('gemini')) {
      return AIProvider.GEMINI;
    }

    // 默认返回 OpenAI
    return AIProvider.OPENAI;
  }

  /**
   * 获取所有适配器的健康状态
   */
  public static async getHealthStatus(): Promise<Record<string, any>> {
    const status: Record<string, any> = {};

    for (const provider of this.getSupportedProviders()) {
      try {
        const adapter = this.getAdapter(provider);
        status[provider] = adapter.healthCheck ? await adapter.healthCheck() : { status: 'up' };
      } catch (error) {
        logger.error(`获取 ${provider} 健康状态失败:`, error);
        status[provider] = {
          status: 'down',
          error: error instanceof Error ? error.message : '未知错误',
        };
      }
    }

    return status;
  }

  /**
   * 检查模型是否为动态配置模型
   */
  public static isDynamicModel(modelName: string): boolean {
    return modelConfigManager.isModelAvailable(modelName);
  }

  /**
   * 获取所有可用的动态模型
   */
  public static getDynamicModels(): DynamicModelConfig[] {
    return modelConfigManager.getActiveModels();
  }

  /**
   * 清理所有适配器实例
   */
  public static clearAdapters(): void {
    this.adapters.clear();
    this.dynamicAdapters.clear();
  }

  /**
   * 清理指定的动态适配器
   */
  public static clearDynamicAdapter(modelName: string): void {
    this.dynamicAdapters.delete(modelName);
  }
}

/**
 * AI 服务管理器
 * 提供高级的 AI 服务管理功能
 */
export class AIServiceManager {
  private static instance: AIServiceManager;
  private adapters: Map<AIProvider, AIAdapter> = new Map();

  private constructor() {
    this.initializeAdapters();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): AIServiceManager {
    if (!this.instance) {
      this.instance = new AIServiceManager();
    }
    return this.instance;
  }

  /**
   * 初始化所有适配器
   */
  private initializeAdapters(): void {
    for (const provider of AdapterFactory.getSupportedProviders()) {
      try {
        this.adapters.set(provider, AdapterFactory.getAdapter(provider));
        logger.info(`${provider} 适配器初始化成功`);
      } catch (error) {
        logger.error(`${provider} 适配器初始化失败:`, error);
      }
    }
  }

  /**
   * 获取适配器
   */
  public getAdapter(provider: AIProvider): AIAdapter {
    const adapter = this.adapters.get(provider);
    if (!adapter) {
      throw new ApiError(`适配器未初始化: ${provider}`, 500, 'ADAPTER_NOT_INITIALIZED');
    }
    return adapter;
  }

  /**
   * 智能路由：根据请求自动选择最佳适配器
   */
  public getOptimalAdapter(model: string, requirements?: {
    streaming?: boolean;
    multimodal?: boolean;
    maxTokens?: number;
  }): AIAdapter {
    // 根据模型名称检测提供商
    const detectedProvider = AdapterFactory.detectProvider(model);

    // 可以在这里添加更复杂的路由逻辑
    // 例如：负载均衡、故障转移、性能优化等

    return this.getAdapter(detectedProvider);
  }

  /**
   * 获取所有适配器的状态
   */
  public async getAllAdaptersStatus(): Promise<Record<string, any>> {
    const status: Record<string, any> = {};

    for (const [provider, adapter] of this.adapters) {
      try {
        status[provider] = adapter.healthCheck ? await adapter.healthCheck() : { status: 'up' };
      } catch (error) {
        status[provider] = {
          status: 'down',
          error: error instanceof Error ? error.message : '未知错误',
        };
      }
    }

    return status;
  }

  /**
   * 重新初始化指定适配器
   */
  public reinitializeAdapter(provider: AIProvider): void {
    try {
      this.adapters.set(provider, AdapterFactory.getAdapter(provider));
      logger.info(`${provider} 适配器重新初始化成功`);
    } catch (error) {
      logger.error(`${provider} 适配器重新初始化失败:`, error);
      throw error;
    }
  }

  /**
   * 获取支持指定模型的适配器列表
   */
  public getAdaptersForModel(model: string): AIAdapter[] {
    const supportedAdapters: AIAdapter[] = [];

    for (const adapter of this.adapters.values()) {
      if ('isModelSupported' in adapter && typeof adapter.isModelSupported === 'function' && adapter.isModelSupported(model)) {
        supportedAdapters.push(adapter);
      }
    }

    return supportedAdapters;
  }
}

// 导出适配器类
export { BaseAdapter } from './base';
export { GeminiAdapter } from './gemini';
export { OpenAIAdapter } from './openai';

// 导出默认实例
export const aiServiceManager = AIServiceManager.getInstance();
