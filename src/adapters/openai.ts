import OpenAI from 'openai';
import { BaseAdapter } from './base';
import { 
  AIProvider, 
  ChatCompletionRequest, 
  ChatCompletionResponse, 
  StreamChunk,
  ApiError 
} from '@/types';
import { openaiConfig } from '@/config';
import { logger } from '@/utils/logger';

/**
 * OpenAI API 适配器
 * 实现与 OpenAI API 的集成，支持流式和非流式响应
 */
export class OpenAIAdapter extends BaseAdapter {
  public readonly provider = AIProvider.OPENAI;
  protected readonly baseUrl = openaiConfig.baseUrl;
  protected readonly apiKey = openaiConfig.apiKey;
  
  private client: OpenAI;

  constructor() {
    super();
    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseUrl,
    });
  }

  /**
   * 生成聊天完成响应
   */
  public async generateCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.validateRequest(request);
      this.logRequest(request, requestId);

      const openaiRequest = this.transformRequest(request);
      
      const response = await this.client.chat.completions.create({
        ...openaiRequest,
        stream: false,
      });

      const transformedResponse = this.transformResponse(response);
      const duration = Date.now() - startTime;
      
      this.logResponse(transformedResponse, requestId, duration);
      
      return transformedResponse;
    } catch (error) {
      this.handleApiError(error, `generateCompletion [${requestId}]`);
    }
  }

  /**
   * 生成流式聊天完成响应
   */
  public async* generateCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.validateRequest(request);
      this.logRequest(request, requestId);

      const openaiRequest = this.transformRequest(request);
      
      const stream = await this.client.chat.completions.create({
        ...openaiRequest,
        stream: true,
      });

      let chunkCount = 0;
      
      for await (const chunk of stream) {
        chunkCount++;
        
        const transformedChunk: StreamChunk = {
          id: chunk.id,
          object: chunk.object,
          created: chunk.created,
          model: chunk.model,
          choices: chunk.choices.map(choice => ({
            index: choice.index,
            delta: {
              role: choice.delta.role,
              content: choice.delta.content || '',
            },
            finish_reason: choice.finish_reason || undefined,
          })),
        };

        yield transformedChunk;
      }

      const duration = Date.now() - startTime;
      logger.info(`[${this.provider}] 流式请求完成`, {
        requestId,
        duration: `${duration}ms`,
        chunkCount,
        model: request.model,
      });

    } catch (error) {
      this.handleApiError(error, `generateCompletionStream [${requestId}]`);
    }
  }

  /**
   * 转换请求格式为 OpenAI 格式
   */
  public transformRequest(request: ChatCompletionRequest): OpenAI.Chat.Completions.ChatCompletionCreateParams {
    const openaiRequest: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
      model: request.model,
      messages: request.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        ...(msg.name && { name: msg.name }),
      })),
    };

    // 添加可选参数
    if (request.temperature !== undefined) {
      openaiRequest.temperature = request.temperature;
    }
    if (request.max_tokens !== undefined) {
      openaiRequest.max_tokens = request.max_tokens;
    }
    if (request.top_p !== undefined) {
      openaiRequest.top_p = request.top_p;
    }
    if (request.frequency_penalty !== undefined) {
      openaiRequest.frequency_penalty = request.frequency_penalty;
    }
    if (request.presence_penalty !== undefined) {
      openaiRequest.presence_penalty = request.presence_penalty;
    }
    if (request.stop !== undefined) {
      openaiRequest.stop = request.stop;
    }

    return openaiRequest;
  }

  /**
   * 转换响应格式为统一格式
   */
  public transformResponse(response: OpenAI.Chat.Completions.ChatCompletion): ChatCompletionResponse {
    return {
      id: response.id,
      object: response.object,
      created: response.created,
      model: response.model,
      choices: response.choices.map(choice => ({
        index: choice.index,
        message: {
          role: choice.message.role,
          content: choice.message.content || '',
          ...(choice.message.name && { name: choice.message.name }),
        },
        finish_reason: choice.finish_reason || 'stop',
      })),
      usage: {
        prompt_tokens: response.usage?.prompt_tokens || 0,
        completion_tokens: response.usage?.completion_tokens || 0,
        total_tokens: response.usage?.total_tokens || 0,
      },
    };
  }

  /**
   * 获取默认模型
   */
  protected getDefaultModel(): string {
    return 'gpt-3.5-turbo';
  }

  /**
   * 获取支持的模型列表
   */
  public getSupportedModels(): string[] {
    return [
      'gpt-4',
      'gpt-4-turbo',
      'gpt-4-turbo-preview',
      'gpt-3.5-turbo',
      'gpt-3.5-turbo-16k',
    ];
  }

  /**
   * 验证模型是否支持
   */
  public isModelSupported(model: string): boolean {
    return this.getSupportedModels().includes(model);
  }

  /**
   * 获取模型信息
   */
  public async getModelInfo(model: string): Promise<any> {
    try {
      const modelInfo = await this.client.models.retrieve(model);
      return modelInfo;
    } catch (error) {
      this.handleApiError(error, `getModelInfo [${model}]`);
    }
  }
}
