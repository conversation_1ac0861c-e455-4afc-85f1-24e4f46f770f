import { VertexAI } from '@google-cloud/vertexai';
import { BaseAdapter } from './base';
import { 
  AIProvider, 
  ChatCompletionRequest, 
  ChatCompletionResponse, 
  StreamChunk,
  ApiError 
} from '@/types';
import { vertexAIConfig } from '@/config';
import { logger } from '@/utils/logger';

/**
 * Google Vertex AI 适配器
 * 实现与 Vertex AI API 的集成，支持流式和非流式响应
 */
export class VertexAIAdapter extends BaseAdapter {
  public readonly provider = AIProvider.VERTEX_AI;
  protected readonly baseUrl = `https://${vertexAIConfig.location}-aiplatform.googleapis.com`;
  protected readonly apiKey = ''; // Vertex AI 使用服务账号认证
  
  private vertexAI: VertexAI;

  constructor() {
    super();
    
    // 初始化 Vertex AI 客户端
    this.vertexAI = new VertexAI({
      project: vertexAIConfig.projectId,
      location: vertexAIConfig.location,
      ...(vertexAIConfig.credentialsPath && {
        keyFilename: vertexAIConfig.credentialsPath,
      }),
    });
  }

  /**
   * 生成聊天完成响应
   */
  public async generateCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.validateRequest(request);
      this.logRequest(request, requestId);

      const model = this.vertexAI.getGenerativeModel({
        model: request.model,
        generationConfig: this.buildGenerationConfig(request),
      });

      const vertexRequest = this.transformRequest(request);
      const response = await model.generateContent(vertexRequest);

      const transformedResponse = this.transformResponse(response.response);
      const duration = Date.now() - startTime;
      
      this.logResponse(transformedResponse, requestId, duration);
      
      return transformedResponse;
    } catch (error) {
      this.handleApiError(error, `generateCompletion [${requestId}]`);
    }
  }

  /**
   * 生成流式聊天完成响应
   */
  public async* generateCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.validateRequest(request);
      this.logRequest(request, requestId);

      const model = this.vertexAI.getGenerativeModel({
        model: request.model,
        generationConfig: this.buildGenerationConfig(request),
      });

      const vertexRequest = this.transformRequest(request);
      const stream = await model.generateContentStream(vertexRequest);

      let chunkCount = 0;
      
      for await (const chunk of stream.stream) {
        chunkCount++;
        
        const text = chunk.candidates?.[0]?.content?.parts?.[0]?.text || '';
        const finishReason = chunk.candidates?.[0]?.finishReason;
        
        const transformedChunk: StreamChunk = {
          id: requestId,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: request.model,
          choices: [{
            index: 0,
            delta: {
              role: 'assistant',
              content: text,
            },
            finish_reason: finishReason ? this.mapFinishReason(finishReason) : undefined,
          }],
        };

        yield transformedChunk;
      }

      const duration = Date.now() - startTime;
      logger.info(`[${this.provider}] 流式请求完成`, {
        requestId,
        duration: `${duration}ms`,
        chunkCount,
        model: request.model,
      });

    } catch (error) {
      this.handleApiError(error, `generateCompletionStream [${requestId}]`);
    }
  }

  /**
   * 构建生成配置
   */
  private buildGenerationConfig(request: ChatCompletionRequest): any {
    const config: any = {};
    
    if (request.temperature !== undefined) {
      config.temperature = request.temperature;
    }
    if (request.max_tokens !== undefined) {
      config.maxOutputTokens = request.max_tokens;
    }
    if (request.top_p !== undefined) {
      config.topP = request.top_p;
    }
    if (request.stop !== undefined) {
      config.stopSequences = Array.isArray(request.stop) ? request.stop : [request.stop];
    }

    return config;
  }

  /**
   * 转换请求格式为 Vertex AI 格式
   */
  public transformRequest(request: ChatCompletionRequest): any {
    // 将消息转换为 Vertex AI 格式
    const contents = [];
    
    for (const message of request.messages) {
      if (message.role === 'system') {
        // 系统消息作为第一个用户消息的前缀
        if (contents.length === 0) {
          contents.push({
            role: 'user',
            parts: [{ text: message.content }],
          });
        } else {
          // 如果已有消息，将系统消息合并到第一个用户消息
          const firstUserMessage = contents.find(c => c.role === 'user');
          if (firstUserMessage) {
            firstUserMessage.parts[0].text = `${message.content}\n\n${firstUserMessage.parts[0].text}`;
          }
        }
      } else {
        contents.push({
          role: message.role === 'assistant' ? 'model' : 'user',
          parts: [{ text: message.content }],
        });
      }
    }

    return { contents };
  }

  /**
   * 转换响应格式为统一格式
   */
  public transformResponse(response: any): ChatCompletionResponse {
    const candidate = response.candidates?.[0];
    const content = candidate?.content?.parts?.[0]?.text || '';
    
    return {
      id: `vertex_${Date.now()}`,
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: response.model || 'gemini-pro',
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content,
        },
        finish_reason: this.mapFinishReason(candidate?.finishReason) || 'stop',
      }],
      usage: {
        prompt_tokens: response.usageMetadata?.promptTokenCount || 0,
        completion_tokens: response.usageMetadata?.candidatesTokenCount || 0,
        total_tokens: response.usageMetadata?.totalTokenCount || 0,
      },
    };
  }

  /**
   * 映射完成原因
   */
  private mapFinishReason(reason: string): string {
    const mapping: Record<string, string> = {
      'FINISH_REASON_STOP': 'stop',
      'FINISH_REASON_MAX_TOKENS': 'length',
      'FINISH_REASON_SAFETY': 'content_filter',
      'FINISH_REASON_RECITATION': 'content_filter',
      'FINISH_REASON_OTHER': 'stop',
    };
    return mapping[reason] || 'stop';
  }

  /**
   * 获取默认模型
   */
  protected getDefaultModel(): string {
    return 'gemini-1.5-pro';
  }

  /**
   * 获取支持的模型列表
   */
  public getSupportedModels(): string[] {
    return [
      'gemini-1.5-pro',
      'gemini-1.5-flash',
      'gemini-1.0-pro',
      'gemini-pro',
      'gemini-pro-vision',
    ];
  }

  /**
   * 验证模型是否支持
   */
  public isModelSupported(model: string): boolean {
    return this.getSupportedModels().includes(model);
  }
}
