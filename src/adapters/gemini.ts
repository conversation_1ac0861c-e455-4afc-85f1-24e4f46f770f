import { GoogleGenAI } from '@google/genai';
import { BaseAdapter } from './base';
import { 
  AIProvider, 
  ChatCompletionRequest, 
  ChatCompletionResponse, 
  StreamChunk,
  ApiError,
  AuthMode 
} from '@/types';
import { geminiConfig } from '@/config';
import { logger } from '@/utils/logger';

/**
 * Google Gemini API 适配器
 * 支持两种认证模式：Bearer Token 和官方 Google 认证
 */
export class GeminiAdapter extends BaseAdapter {
  public readonly provider = AIProvider.GEMINI;
  protected readonly baseUrl = geminiConfig.baseUrl;
  protected readonly apiKey = geminiConfig.apiKey;
  
  private client: GoogleGenAI;

  constructor() {
    super();
    this.client = new GoogleGenAI({
      apiKey: this.apiKey,
    });
  }

  /**
   * 生成聊天完成响应
   */
  public async generateCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.validateRequest(request);
      this.logRequest(request, requestId);

      const geminiRequest = this.transformRequest(request);
      
      // 根据认证模式选择不同的请求方式
      let response;
      if (request.auth_mode === AuthMode.BEARER_TOKEN) {
        response = await this.generateWithBearerAuth(geminiRequest, request);
      } else {
        response = await this.generateWithGoogleAuth(geminiRequest, request);
      }

      const transformedResponse = this.transformResponse(response);
      const duration = Date.now() - startTime;
      
      this.logResponse(transformedResponse, requestId, duration);
      
      return transformedResponse;
    } catch (error) {
      this.handleApiError(error, `generateCompletion [${requestId}]`);
    }
  }

  /**
   * 生成流式聊天完成响应
   */
  public async* generateCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      this.validateRequest(request);
      this.logRequest(request, requestId);

      const geminiRequest = this.transformRequest(request);
      
      let stream;
      if (request.auth_mode === AuthMode.BEARER_TOKEN) {
        stream = await this.generateStreamWithBearerAuth(geminiRequest, request);
      } else {
        stream = await this.generateStreamWithGoogleAuth(geminiRequest, request);
      }

      let chunkCount = 0;
      
      for await (const chunk of stream) {
        chunkCount++;
        
        const transformedChunk: StreamChunk = {
          id: requestId,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: request.model,
          choices: [{
            index: 0,
            delta: {
              role: 'assistant',
              content: chunk.text || '',
            },
            finish_reason: chunk.finishReason ? this.mapFinishReason(chunk.finishReason) : undefined,
          }],
        };

        yield transformedChunk;
      }

      const duration = Date.now() - startTime;
      logger.info(`[${this.provider}] 流式请求完成`, {
        requestId,
        duration: `${duration}ms`,
        chunkCount,
        model: request.model,
      });

    } catch (error) {
      this.handleApiError(error, `generateCompletionStream [${requestId}]`);
    }
  }

  /**
   * 使用 Bearer Token 认证生成内容
   */
  private async generateWithBearerAuth(geminiRequest: any, request: ChatCompletionRequest): Promise<any> {
    // 实现 Bearer Token 认证方式（类似 vertex2openai 项目）
    const url = `${this.baseUrl}/v1beta/models/${request.model}:generateContent`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(geminiRequest),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * 使用 Bearer Token 认证生成流式内容
   */
  private async generateStreamWithBearerAuth(geminiRequest: any, request: ChatCompletionRequest): Promise<AsyncGenerator<any>> {
    const url = `${this.baseUrl}/v1beta/models/${request.model}:streamGenerateContent`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(geminiRequest),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return this.parseStreamResponse(response);
  }

  /**
   * 使用官方 Google 认证生成内容
   */
  private async generateWithGoogleAuth(geminiRequest: any, request: ChatCompletionRequest): Promise<any> {
    const response = await this.client.models.generateContent({
      model: request.model,
      contents: geminiRequest.contents,
      config: geminiRequest.generationConfig,
    });

    return response;
  }

  /**
   * 使用官方 Google 认证生成流式内容
   */
  private async generateStreamWithGoogleAuth(geminiRequest: any, request: ChatCompletionRequest): Promise<AsyncGenerator<any>> {
    const stream = await this.client.models.generateContentStream({
      model: request.model,
      contents: geminiRequest.contents,
      config: geminiRequest.generationConfig,
    });

    return stream;
  }

  /**
   * 解析流式响应
   */
  private async* parseStreamResponse(response: Response): AsyncGenerator<any> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法读取响应流');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() && line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.candidates && data.candidates[0]) {
                yield {
                  text: data.candidates[0].content?.parts?.[0]?.text || '',
                  finishReason: data.candidates[0].finishReason,
                };
              }
            } catch (e) {
              logger.warn('解析流式数据失败:', line);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * 转换请求格式为 Gemini 格式
   */
  public transformRequest(request: ChatCompletionRequest): any {
    const contents = request.messages.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }],
    }));

    const generationConfig: any = {};
    
    if (request.temperature !== undefined) {
      generationConfig.temperature = request.temperature;
    }
    if (request.max_tokens !== undefined) {
      generationConfig.maxOutputTokens = request.max_tokens;
    }
    if (request.top_p !== undefined) {
      generationConfig.topP = request.top_p;
    }
    if (request.stop !== undefined) {
      generationConfig.stopSequences = Array.isArray(request.stop) ? request.stop : [request.stop];
    }

    return {
      contents,
      generationConfig,
    };
  }

  /**
   * 转换响应格式为统一格式
   */
  public transformResponse(response: any): ChatCompletionResponse {
    const candidate = response.candidates?.[0] || response.output?.[0];
    const content = candidate?.content?.parts?.[0]?.text || candidate?.text || '';
    
    return {
      id: `gemini_${Date.now()}`,
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: response.model || 'gemini-pro',
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content,
        },
        finish_reason: this.mapFinishReason(candidate?.finishReason) || 'stop',
      }],
      usage: {
        prompt_tokens: response.usageMetadata?.promptTokenCount || 0,
        completion_tokens: response.usageMetadata?.candidatesTokenCount || 0,
        total_tokens: response.usageMetadata?.totalTokenCount || 0,
      },
    };
  }

  /**
   * 映射完成原因
   */
  private mapFinishReason(reason: string): string {
    const mapping: Record<string, string> = {
      'FINISH_REASON_STOP': 'stop',
      'FINISH_REASON_MAX_TOKENS': 'length',
      'FINISH_REASON_SAFETY': 'content_filter',
      'FINISH_REASON_RECITATION': 'content_filter',
    };
    return mapping[reason] || 'stop';
  }

  /**
   * 获取默认模型
   */
  protected getDefaultModel(): string {
    return 'gemini-pro';
  }

  /**
   * 获取支持的模型列表
   */
  public getSupportedModels(): string[] {
    return [
      'gemini-pro',
      'gemini-pro-vision',
      'gemini-1.5-pro',
      'gemini-1.5-flash',
      'gemini-2.0-flash',
    ];
  }
}
