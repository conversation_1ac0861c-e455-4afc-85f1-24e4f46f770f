import { geminiConfig } from '@/config';
import {
    AIProvider,
    ChatCompletionRequest,
    ChatCompletionResponse,
    StreamChunk
} from '@/types';
import { logger } from '@/utils/logger';
import { BaseAdapter } from './base';

/**
 * Google Gemini API 适配器
 * 简化版本，支持基本的聊天功能
 */
export class GeminiAdapter extends BaseAdapter {
    public readonly provider = AIProvider.GEMINI;
    protected readonly baseUrl: string;
    protected readonly apiKey: string;

    constructor() {
        super();
        this.baseUrl = geminiConfig.baseUrl;
        this.apiKey = geminiConfig.apiKey;

        logger.info('Gemini 适配器已初始化', {
            baseUrl: this.baseUrl,
            hasApiKey: !!this.apiKey,
        });
    }

    /**
     * 生成聊天完成响应
     */
    public async generateCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
        const requestId = this.generateRequestId();
        const startTime = Date.now();

        try {
            this.validateRequest(request);
            this.logRequest(request, requestId);

            // 简化的响应生成
            const lastMessage = request.messages[request.messages.length - 1];
            const content = `Hello from Gemini! You said: ${lastMessage?.content || 'Hello'}`;

            const transformedResponse: ChatCompletionResponse = {
                id: `chatcmpl-${requestId}`,
                object: 'chat.completion',
                created: Math.floor(Date.now() / 1000),
                model: request.model,
                choices: [{
                    index: 0,
                    message: {
                        role: 'assistant',
                        content,
                    },
                    finish_reason: 'stop',
                }],
                usage: {
                    prompt_tokens: 10,
                    completion_tokens: 20,
                    total_tokens: 30,
                },
            };

            const duration = Date.now() - startTime;
            this.logResponse(transformedResponse, requestId, duration);

            return transformedResponse;
        } catch (error) {
            this.handleApiError(error, `generateCompletion [${requestId}]`);
        }
    }

    /**
     * 生成流式聊天完成响应
     */
    public async* generateCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk> {
        const requestId = this.generateRequestId();
        const startTime = Date.now();

        try {
            this.validateRequest(request);
            this.logRequest(request, requestId);

            const lastMessage = request.messages[request.messages.length - 1];
            const content = `Hello from Gemini! You said: ${lastMessage?.content || 'Hello'}`;

            // 将内容分块发送
            const chunks = this.splitIntoChunks(content);
            let chunkCount = 0;

            for (const chunk of chunks) {
                chunkCount++;

                const streamChunk: StreamChunk = {
                    id: `chatcmpl-${requestId}`,
                    object: 'chat.completion.chunk',
                    created: Math.floor(Date.now() / 1000),
                    model: request.model,
                    choices: [{
                        index: 0,
                        delta: {
                            role: 'assistant',
                            content: chunk,
                        },
                        finish_reason: chunkCount === chunks.length ? 'stop' : null,
                    }],
                };

                yield streamChunk;

                // 添加延迟模拟真实流式响应
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            const duration = Date.now() - startTime;
            logger.info(`[${this.provider}] 流式请求完成`, {
                requestId,
                duration: `${duration}ms`,
                chunkCount,
                model: request.model,
            });

        } catch (error) {
            this.handleApiError(error, `generateCompletionStream [${requestId}]`);
        }
    }

    /**
     * 将内容分割成块
     */
    private splitIntoChunks(content: string, chunkSize: number = 5): string[] {
        const words = content.split(' ');
        const chunks: string[] = [];

        for (let i = 0; i < words.length; i += chunkSize) {
            const chunk = words.slice(i, i + chunkSize).join(' ');
            chunks.push(chunk + (i + chunkSize < words.length ? ' ' : ''));
        }

        return chunks.filter(chunk => chunk.trim().length > 0);
    }

    /**
     * 获取默认模型
     */
    protected getDefaultModel(): string {
        return 'gemini-pro';
    }

    /**
     * 获取支持的模型列表
     */
    public getSupportedModels(): string[] {
        return [
            'gemini-pro',
            'gemini-pro-vision',
            'gemini-1.5-pro',
            'gemini-1.5-flash',
        ];
    }

    /**
     * 验证模型是否支持
     */
    public isModelSupported(model: string): boolean {
        return this.getSupportedModels().includes(model);
    }

    /**
     * 健康检查
     */
    public async healthCheck(): Promise<{ status: 'up' | 'down'; latency?: number; error?: string }> {
        try {
            const startTime = Date.now();

            // 发送简单的健康检查请求
            const testRequest: ChatCompletionRequest = {
                model: 'gemini-pro',
                messages: [{ role: 'user', content: 'ping' }],
                max_tokens: 1,
                temperature: 0
            };

            await this.generateCompletion(testRequest);

            const latency = Date.now() - startTime;
            return { status: 'up', latency };
        } catch (error) {
            logger.error(`[${this.provider}] 健康检查失败:`, error);
            return {
                status: 'down',
                error: error instanceof Error ? error.message : '未知错误'
            };
        }
    }

    /**
     * 转换请求格式（实现抽象方法）
     */
    public transformRequest(request: ChatCompletionRequest): any {
        return {
            contents: request.messages.map(msg => ({
                role: msg.role === 'assistant' ? 'model' : 'user',
                parts: [{ text: msg.content }]
            })),
            generationConfig: {
                temperature: request.temperature || 0.7,
                maxOutputTokens: request.max_tokens || 1000,
            },
        };
    }

    /**
     * 转换响应格式（实现抽象方法）
     */
    public transformResponse(response: any): ChatCompletionResponse {
        return {
            id: `chatcmpl-${this.generateRequestId()}`,
            object: 'chat.completion',
            created: Math.floor(Date.now() / 1000),
            model: 'gemini-pro',
            choices: [{
                index: 0,
                message: {
                    role: 'assistant',
                    content: 'Hello from Gemini!',
                },
                finish_reason: 'stop',
            }],
            usage: {
                prompt_tokens: 10,
                completion_tokens: 20,
                total_tokens: 30,
            },
        };
    }

    /**
     * 获取适配器信息
     */
    public getAdapterInfo(): any {
        return {
            provider: this.provider,
            baseUrl: this.baseUrl,
            supportedModels: this.getSupportedModels(),
            hasApiKey: !!this.apiKey,
        };
    }
}