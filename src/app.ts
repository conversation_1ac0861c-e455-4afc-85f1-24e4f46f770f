import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { config, validateConfig } from '@/config';
import { logger } from '@/utils/logger';
import { sseManager } from '@/utils/sse';

// 中间件导入
import {
  requestId,
  requestLogger,
  errorHandler,
  responseHelpers,
} from '@/middleware';

// 路由导入
import routes from '@/routes';

/**
 * 创建 Express 应用程序
 */
export function createApp(): express.Application {
  // 验证配置
  validateConfig();

  const app = express();

  // 信任代理（用于获取真实 IP）
  app.set('trust proxy', true);

  // 安全中间件
  app.use(helmet({
    contentSecurityPolicy: false, // 禁用 CSP 以支持 SSE
    crossOriginEmbedderPolicy: false, // 禁用 COEP 以支持跨域
  }));

  // CORS 配置
  app.use(cors({
    origin: config.server.corsOrigin === '*' ? true : config.server.corsOrigin.split(','),
    credentials: config.server.corsCredentials,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-API-Key',
      'X-Request-ID',
      'Accept',
      'Cache-Control',
    ],
    exposedHeaders: [
      'X-Request-ID',
      'X-RateLimit-Limit',
      'X-RateLimit-Remaining',
      'X-RateLimit-Reset',
    ],
  }));

  // 压缩中间件
  app.use(compression({
    filter: (req, res) => {
      // 不压缩 SSE 响应
      if (req.headers.accept?.includes('text/event-stream')) {
        return false;
      }
      return compression.filter(req, res);
    },
  }));

  // 请求解析中间件
  app.use(express.json({ 
    limit: '10mb',
    verify: (req, res, buf) => {
      // 验证 JSON 格式
      try {
        JSON.parse(buf.toString());
      } catch (error) {
        throw new Error('无效的 JSON 格式');
      }
    },
  }));
  
  app.use(express.urlencoded({ 
    extended: true, 
    limit: '10mb' 
  }));

  // 自定义中间件
  app.use(requestId);
  app.use(responseHelpers);
  app.use(requestLogger);

  // 注册路由
  app.use('/', routes);

  // 全局错误处理中间件（必须在最后）
  app.use(errorHandler);

  return app;
}

/**
 * 启动服务器
 */
export function startServer(): void {
  const app = createApp();
  const { port, host } = config.server;

  const server = app.listen(port, host, () => {
    logger.info('🚀 AI Proxy Server 启动成功', {
      port,
      host,
      nodeEnv: config.server.nodeEnv,
      version: '1.0.0',
      endpoints: {
        api: `http://${host}:${port}/v1`,
        health: `http://${host}:${port}/health`,
        docs: `http://${host}:${port}/docs`,
      },
    });

    // 输出配置信息
    logger.info('服务配置信息', {
      cors: {
        origin: config.server.corsOrigin,
        credentials: config.server.corsCredentials,
      },
      rateLimit: {
        windowMs: config.server.rateLimitWindowMs,
        maxRequests: config.server.rateLimitMaxRequests,
      },
      providers: {
        openai: !!config.openai.apiKey,
        gemini: !!config.gemini.apiKey,
        vertexAI: !!config.vertexAI.projectId,
      },
    });
  });

  // 优雅关闭处理
  const gracefulShutdown = (signal: string) => {
    logger.info(`收到 ${signal} 信号，开始优雅关闭...`);
    
    server.close((err) => {
      if (err) {
        logger.error('服务器关闭时发生错误:', err);
        process.exit(1);
      }

      logger.info('HTTP 服务器已关闭');

      // 清理 SSE 连接
      sseManager.cleanup();

      // 关闭数据库连接等其他资源
      // 这里可以添加其他清理逻辑

      logger.info('所有资源已清理，进程即将退出');
      process.exit(0);
    });

    // 强制退出超时
    setTimeout(() => {
      logger.error('强制退出：优雅关闭超时');
      process.exit(1);
    }, 10000);
  };

  // 监听进程信号
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  // 监听未捕获的异常
  process.on('uncaughtException', (error) => {
    logger.error('未捕获的异常:', error);
    gracefulShutdown('uncaughtException');
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('未处理的 Promise 拒绝:', { reason, promise });
    gracefulShutdown('unhandledRejection');
  });

  // 监听内存警告
  process.on('warning', (warning) => {
    logger.warn('进程警告:', {
      name: warning.name,
      message: warning.message,
      stack: warning.stack,
    });
  });

  return server;
}

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
  startServer();
}

export default createApp;
