import { Router } from 'express';
import { ExtendedRequest, ExtendedResponse, AIProvider, DynamicModelConfig } from '@/types';
import { aiService } from '@/services/ai-service';
import { modelConfigManager } from '@/config/models';
import { asyncHandler } from '@/middleware/error-handler';
import { validateModelList, validateProviderParams } from '@/middleware/validation';
import { optionalAuth, smartAuth } from '@/middleware/auth';
import { lenientRateLimit } from '@/middleware/rate-limiter';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * 获取支持的模型列表
 * GET /v1/models
 * 
 * 查询参数：
 * - provider: 指定提供商 (可选)
 * - search: 搜索关键词 (可选)
 * - page: 页码 (可选，默认 1)
 * - limit: 每页数量 (可选，默认 20)
 */
router.get('/',
  lenientRateLimit,
  optionalAuth,
  validateModelList,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { provider, search, page = 1, limit = 20 } = req.query;
    
    logger.info('获取模型列表请求', {
      requestId: req.requestId,
      provider,
      search,
      page,
      limit,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      // 获取模型列表
      const allModels = await aiService.getSupportedModels(provider as AIProvider);
      
      // 搜索过滤
      let filteredModels = allModels;
      if (search && typeof search === 'string') {
        const searchLower = search.toLowerCase();
        filteredModels = allModels.filter(model => 
          model.id.toLowerCase().includes(searchLower) ||
          model.name.toLowerCase().includes(searchLower) ||
          model.provider.toLowerCase().includes(searchLower)
        );
      }

      // 分页
      const pageNum = parseInt(page as string, 10);
      const limitNum = parseInt(limit as string, 10);
      const startIndex = (pageNum - 1) * limitNum;
      const endIndex = startIndex + limitNum;
      
      const paginatedModels = filteredModels.slice(startIndex, endIndex);
      
      // 构建响应
      const response = {
        object: 'list',
        data: paginatedModels.map(model => ({
          id: model.id,
          object: 'model',
          created: Math.floor(Date.now() / 1000),
          owned_by: model.provider,
          provider: model.provider,
          name: model.name,
          description: `${model.provider} 模型: ${model.name}`,
        })),
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: filteredModels.length,
          totalPages: Math.ceil(filteredModels.length / limitNum),
          hasNext: endIndex < filteredModels.length,
          hasPrev: pageNum > 1,
        },
      };

      logger.info('模型列表获取成功', {
        requestId: req.requestId,
        totalModels: allModels.length,
        filteredModels: filteredModels.length,
        returnedModels: paginatedModels.length,
      });

      res.sendSuccess(response, '模型列表获取成功');
    } catch (error) {
      logger.error('获取模型列表失败', {
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 获取指定模型的详细信息
 * GET /v1/models/:id
 */
router.get('/:id',
  lenientRateLimit,
  optionalAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { id } = req.params;
    
    logger.info('获取模型详情请求', {
      requestId: req.requestId,
      modelId: id,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      // 获取所有模型
      const allModels = await aiService.getSupportedModels();
      
      // 查找指定模型
      const model = allModels.find(m => m.id === id);
      
      if (!model) {
        return res.sendError(`模型 ${id} 不存在`, 404);
      }

      // 验证模型是否支持
      const isSupported = await aiService.validateModel(id, model.provider);
      
      const response = {
        id: model.id,
        object: 'model',
        created: Math.floor(Date.now() / 1000),
        owned_by: model.provider,
        provider: model.provider,
        name: model.name,
        description: `${model.provider} 模型: ${model.name}`,
        supported: isSupported,
        capabilities: {
          completion: true,
          streaming: true,
          chat: true,
        },
      };

      logger.info('模型详情获取成功', {
        requestId: req.requestId,
        modelId: id,
        provider: model.provider,
        supported: isSupported,
      });

      res.sendSuccess(response, '模型详情获取成功');
    } catch (error) {
      logger.error('获取模型详情失败', {
        requestId: req.requestId,
        modelId: id,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 获取指定提供商的模型列表
 * GET /v1/models/provider/:provider
 */
router.get('/provider/:provider',
  lenientRateLimit,
  optionalAuth,
  validateProviderParams,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { provider } = req.params;
    
    logger.info('获取提供商模型列表请求', {
      requestId: req.requestId,
      provider,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      const models = await aiService.getSupportedModels(provider as AIProvider);
      
      const response = {
        object: 'list',
        provider,
        data: models.map(model => ({
          id: model.id,
          object: 'model',
          created: Math.floor(Date.now() / 1000),
          owned_by: model.provider,
          provider: model.provider,
          name: model.name,
          description: `${model.provider} 模型: ${model.name}`,
        })),
        total: models.length,
      };

      logger.info('提供商模型列表获取成功', {
        requestId: req.requestId,
        provider,
        modelCount: models.length,
      });

      res.sendSuccess(response, `${provider} 模型列表获取成功`);
    } catch (error) {
      logger.error('获取提供商模型列表失败', {
        requestId: req.requestId,
        provider,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 验证模型是否支持
 * POST /v1/models/validate
 */
router.post('/validate',
  lenientRateLimit,
  optionalAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { model, provider } = req.body;
    
    if (!model) {
      return res.sendError('模型名称不能为空', 400);
    }

    logger.info('模型验证请求', {
      requestId: req.requestId,
      model,
      provider,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      const isSupported = await aiService.validateModel(model, provider);
      
      const response = {
        model,
        provider,
        supported: isSupported,
        timestamp: new Date().toISOString(),
      };

      logger.info('模型验证完成', {
        requestId: req.requestId,
        model,
        provider,
        supported: isSupported,
      });

      res.sendSuccess(response, '模型验证完成');
    } catch (error) {
      logger.error('模型验证失败', {
        requestId: req.requestId,
        model,
        provider,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 获取模型统计信息
 * GET /v1/models/stats
 */
router.get('/stats',
  lenientRateLimit,
  optionalAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    logger.info('获取模型统计信息请求', {
      requestId: req.requestId,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      const allModels = await aiService.getSupportedModels();
      
      // 按提供商统计
      const providerStats = allModels.reduce((stats, model) => {
        stats[model.provider] = (stats[model.provider] || 0) + 1;
        return stats;
      }, {} as Record<string, number>);

      const response = {
        total: allModels.length,
        byProvider: providerStats,
        providers: Object.keys(providerStats),
        timestamp: new Date().toISOString(),
      };

      logger.info('模型统计信息获取成功', {
        requestId: req.requestId,
        totalModels: allModels.length,
        providers: Object.keys(providerStats),
      });

      res.sendSuccess(response, '模型统计信息获取成功');
    } catch (error) {
      logger.error('获取模型统计信息失败', {
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 获取动态模型配置列表
 * GET /v1/models/dynamic
 */
router.get('/dynamic',
  lenientRateLimit,
  optionalAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    logger.info('获取动态模型配置列表', {
      requestId: req.requestId,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      const dynamicModels = modelConfigManager.getActiveModels();

      const response = {
        object: 'list',
        data: dynamicModels.map(config => ({
          id: config.name,
          object: 'model',
          name: config.name,
          provider: config.provider,
          actualModel: config.modelName,
          baseUrl: config.baseUrl,
          supportsVision: config.supportsVision,
          isActive: config.isActive,
          description: config.description,
          isDynamic: true,
        })),
        total: dynamicModels.length,
      };

      logger.info('动态模型配置列表获取成功', {
        requestId: req.requestId,
        modelCount: dynamicModels.length,
      });

      res.sendSuccess(response, '动态模型配置列表获取成功');
    } catch (error) {
      logger.error('获取动态模型配置列表失败', {
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 添加动态模型配置
 * POST /v1/models/dynamic
 */
router.post('/dynamic',
  lenientRateLimit,
  smartAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const modelConfig: DynamicModelConfig = req.body;

    logger.info('添加动态模型配置', {
      requestId: req.requestId,
      modelName: modelConfig.name,
      provider: modelConfig.provider,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      // 验证配置
      const errors = modelConfigManager.validateModelConfig(modelConfig);
      if (errors.length > 0) {
        return res.sendError(`配置验证失败: ${errors.join(', ')}`, 400);
      }

      // 检查模型名称是否已存在
      if (modelConfigManager.isModelAvailable(modelConfig.name)) {
        return res.sendError(`模型名称已存在: ${modelConfig.name}`, 409);
      }

      // 注册模型
      modelConfigManager.registerModel(modelConfig);

      logger.info('动态模型配置添加成功', {
        requestId: req.requestId,
        modelName: modelConfig.name,
        provider: modelConfig.provider,
      });

      res.sendSuccess({
        name: modelConfig.name,
        provider: modelConfig.provider,
        isActive: modelConfig.isActive,
      }, '动态模型配置添加成功');
    } catch (error) {
      logger.error('添加动态模型配置失败', {
        requestId: req.requestId,
        modelName: modelConfig.name,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 更新动态模型配置
 * PUT /v1/models/dynamic/:name
 */
router.put('/dynamic/:name',
  lenientRateLimit,
  smartAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { name } = req.params;
    const updates: Partial<DynamicModelConfig> = req.body;

    logger.info('更新动态模型配置', {
      requestId: req.requestId,
      modelName: name,
      updates: Object.keys(updates),
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      const success = modelConfigManager.updateModel(name, updates);
      if (!success) {
        return res.sendError(`模型配置不存在: ${name}`, 404);
      }

      logger.info('动态模型配置更新成功', {
        requestId: req.requestId,
        modelName: name,
      });

      res.sendSuccess({ name, updated: true }, '动态模型配置更新成功');
    } catch (error) {
      logger.error('更新动态模型配置失败', {
        requestId: req.requestId,
        modelName: name,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 删除动态模型配置
 * DELETE /v1/models/dynamic/:name
 */
router.delete('/dynamic/:name',
  lenientRateLimit,
  smartAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { name } = req.params;

    logger.info('删除动态模型配置', {
      requestId: req.requestId,
      modelName: name,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      const success = modelConfigManager.removeModel(name);
      if (!success) {
        return res.sendError(`模型配置不存在: ${name}`, 404);
      }

      logger.info('动态模型配置删除成功', {
        requestId: req.requestId,
        modelName: name,
      });

      res.sendSuccess({ name, deleted: true }, '动态模型配置删除成功');
    } catch (error) {
      logger.error('删除动态模型配置失败', {
        requestId: req.requestId,
        modelName: name,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 激活/停用动态模型
 * PATCH /v1/models/dynamic/:name/toggle
 */
router.patch('/dynamic/:name/toggle',
  lenientRateLimit,
  smartAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { name } = req.params;
    const { isActive } = req.body;

    if (typeof isActive !== 'boolean') {
      return res.sendError('isActive 参数必须是布尔值', 400);
    }

    logger.info('切换动态模型状态', {
      requestId: req.requestId,
      modelName: name,
      isActive,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      const success = modelConfigManager.setModelActive(name, isActive);
      if (!success) {
        return res.sendError(`模型配置不存在: ${name}`, 404);
      }

      logger.info('动态模型状态切换成功', {
        requestId: req.requestId,
        modelName: name,
        isActive,
      });

      res.sendSuccess({
        name,
        isActive,
        action: isActive ? 'activated' : 'deactivated'
      }, `模型已${isActive ? '激活' : '停用'}`);
    } catch (error) {
      logger.error('切换动态模型状态失败', {
        requestId: req.requestId,
        modelName: name,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

export default router;
