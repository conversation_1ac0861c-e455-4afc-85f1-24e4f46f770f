import { Router } from 'express';
import { ExtendedRequest, ExtendedResponse, AIProvider } from '@/types';
import { aiService } from '@/services/ai-service';
import { asyncHandler } from '@/middleware/error-handler';
import { validateModelList, validateProviderParams } from '@/middleware/validation';
import { optionalAuth } from '@/middleware/auth';
import { lenientRateLimit } from '@/middleware/rate-limiter';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * 获取支持的模型列表
 * GET /v1/models
 * 
 * 查询参数：
 * - provider: 指定提供商 (可选)
 * - search: 搜索关键词 (可选)
 * - page: 页码 (可选，默认 1)
 * - limit: 每页数量 (可选，默认 20)
 */
router.get('/',
  lenientRateLimit,
  optionalAuth,
  validateModelList,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { provider, search, page = 1, limit = 20 } = req.query;
    
    logger.info('获取模型列表请求', {
      requestId: req.requestId,
      provider,
      search,
      page,
      limit,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      // 获取模型列表
      const allModels = await aiService.getSupportedModels(provider as AIProvider);
      
      // 搜索过滤
      let filteredModels = allModels;
      if (search && typeof search === 'string') {
        const searchLower = search.toLowerCase();
        filteredModels = allModels.filter(model => 
          model.id.toLowerCase().includes(searchLower) ||
          model.name.toLowerCase().includes(searchLower) ||
          model.provider.toLowerCase().includes(searchLower)
        );
      }

      // 分页
      const pageNum = parseInt(page as string, 10);
      const limitNum = parseInt(limit as string, 10);
      const startIndex = (pageNum - 1) * limitNum;
      const endIndex = startIndex + limitNum;
      
      const paginatedModels = filteredModels.slice(startIndex, endIndex);
      
      // 构建响应
      const response = {
        object: 'list',
        data: paginatedModels.map(model => ({
          id: model.id,
          object: 'model',
          created: Math.floor(Date.now() / 1000),
          owned_by: model.provider,
          provider: model.provider,
          name: model.name,
          description: `${model.provider} 模型: ${model.name}`,
        })),
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: filteredModels.length,
          totalPages: Math.ceil(filteredModels.length / limitNum),
          hasNext: endIndex < filteredModels.length,
          hasPrev: pageNum > 1,
        },
      };

      logger.info('模型列表获取成功', {
        requestId: req.requestId,
        totalModels: allModels.length,
        filteredModels: filteredModels.length,
        returnedModels: paginatedModels.length,
      });

      res.sendSuccess(response, '模型列表获取成功');
    } catch (error) {
      logger.error('获取模型列表失败', {
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 获取指定模型的详细信息
 * GET /v1/models/:id
 */
router.get('/:id',
  lenientRateLimit,
  optionalAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { id } = req.params;
    
    logger.info('获取模型详情请求', {
      requestId: req.requestId,
      modelId: id,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      // 获取所有模型
      const allModels = await aiService.getSupportedModels();
      
      // 查找指定模型
      const model = allModels.find(m => m.id === id);
      
      if (!model) {
        return res.sendError(`模型 ${id} 不存在`, 404);
      }

      // 验证模型是否支持
      const isSupported = await aiService.validateModel(id, model.provider);
      
      const response = {
        id: model.id,
        object: 'model',
        created: Math.floor(Date.now() / 1000),
        owned_by: model.provider,
        provider: model.provider,
        name: model.name,
        description: `${model.provider} 模型: ${model.name}`,
        supported: isSupported,
        capabilities: {
          completion: true,
          streaming: true,
          chat: true,
        },
      };

      logger.info('模型详情获取成功', {
        requestId: req.requestId,
        modelId: id,
        provider: model.provider,
        supported: isSupported,
      });

      res.sendSuccess(response, '模型详情获取成功');
    } catch (error) {
      logger.error('获取模型详情失败', {
        requestId: req.requestId,
        modelId: id,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 获取指定提供商的模型列表
 * GET /v1/models/provider/:provider
 */
router.get('/provider/:provider',
  lenientRateLimit,
  optionalAuth,
  validateProviderParams,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { provider } = req.params;
    
    logger.info('获取提供商模型列表请求', {
      requestId: req.requestId,
      provider,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      const models = await aiService.getSupportedModels(provider as AIProvider);
      
      const response = {
        object: 'list',
        provider,
        data: models.map(model => ({
          id: model.id,
          object: 'model',
          created: Math.floor(Date.now() / 1000),
          owned_by: model.provider,
          provider: model.provider,
          name: model.name,
          description: `${model.provider} 模型: ${model.name}`,
        })),
        total: models.length,
      };

      logger.info('提供商模型列表获取成功', {
        requestId: req.requestId,
        provider,
        modelCount: models.length,
      });

      res.sendSuccess(response, `${provider} 模型列表获取成功`);
    } catch (error) {
      logger.error('获取提供商模型列表失败', {
        requestId: req.requestId,
        provider,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 验证模型是否支持
 * POST /v1/models/validate
 */
router.post('/validate',
  lenientRateLimit,
  optionalAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { model, provider } = req.body;
    
    if (!model) {
      return res.sendError('模型名称不能为空', 400);
    }

    logger.info('模型验证请求', {
      requestId: req.requestId,
      model,
      provider,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      const isSupported = await aiService.validateModel(model, provider);
      
      const response = {
        model,
        provider,
        supported: isSupported,
        timestamp: new Date().toISOString(),
      };

      logger.info('模型验证完成', {
        requestId: req.requestId,
        model,
        provider,
        supported: isSupported,
      });

      res.sendSuccess(response, '模型验证完成');
    } catch (error) {
      logger.error('模型验证失败', {
        requestId: req.requestId,
        model,
        provider,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 获取模型统计信息
 * GET /v1/models/stats
 */
router.get('/stats',
  lenientRateLimit,
  optionalAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    logger.info('获取模型统计信息请求', {
      requestId: req.requestId,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      const allModels = await aiService.getSupportedModels();
      
      // 按提供商统计
      const providerStats = allModels.reduce((stats, model) => {
        stats[model.provider] = (stats[model.provider] || 0) + 1;
        return stats;
      }, {} as Record<string, number>);

      const response = {
        total: allModels.length,
        byProvider: providerStats,
        providers: Object.keys(providerStats),
        timestamp: new Date().toISOString(),
      };

      logger.info('模型统计信息获取成功', {
        requestId: req.requestId,
        totalModels: allModels.length,
        providers: Object.keys(providerStats),
      });

      res.sendSuccess(response, '模型统计信息获取成功');
    } catch (error) {
      logger.error('获取模型统计信息失败', {
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

export default router;
