import { Router } from 'express';
import chatRoutes from './chat';
import healthRoutes from './health';
import modelsRoutes from './models';
import { notFoundHandler } from '@/middleware/error-handler';

/**
 * 主路由配置
 */
const router = Router();

// API 版本前缀
const API_VERSION = '/v1';

// 注册子路由
router.use(`${API_VERSION}/chat`, chatRoutes);
router.use(`${API_VERSION}/models`, modelsRoutes);
router.use('/health', healthRoutes);

// 根路径信息
router.get('/', (req, res) => {
  res.json({
    name: 'AI Proxy Server',
    version: '1.0.0',
    description: '专业的 AI 代理服务后端 API 系统',
    endpoints: {
      chat: `${API_VERSION}/chat/completions`,
      models: `${API_VERSION}/models`,
      health: '/health',
    },
    documentation: '/docs',
    timestamp: new Date().toISOString(),
  });
});

// API 版本信息
router.get(API_VERSION, (req, res) => {
  res.json({
    version: 'v1',
    description: 'AI Proxy Server API v1',
    endpoints: [
      {
        path: '/chat/completions',
        method: 'POST',
        description: '聊天完成 API',
        supports: ['OpenAI', 'Gemini', 'Vertex AI'],
      },
      {
        path: '/models',
        method: 'GET',
        description: '获取支持的模型列表',
      },
    ],
    timestamp: new Date().toISOString(),
  });
});

// 404 处理
router.use('*', notFoundHandler);

export default router;
