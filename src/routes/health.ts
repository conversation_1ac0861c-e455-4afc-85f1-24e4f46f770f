import { Router } from 'express';
import { ExtendedRequest, ExtendedResponse, HealthStatus } from '@/types';
import { aiService } from '@/services/ai-service';
import { sseManager } from '@/utils/sse';
import { asyncHandler } from '@/middleware/error-handler';
import { validateHealthCheck } from '@/middleware/validation';
import { lenientRateLimit } from '@/middleware/rate-limiter';
import { logger } from '@/utils/logger';
import { config } from '@/config';

const router = Router();

// 服务启动时间
const startTime = Date.now();

/**
 * 基础健康检查
 * GET /health
 */
router.get('/',
  lenientRateLimit,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const uptime = Date.now() - startTime;
    
    const healthStatus: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime,
      version: '1.0.0',
      services: {
        api: {
          status: 'up',
        },
      },
    };

    res.sendSuccess(healthStatus, '服务健康');
  })
);

/**
 * 详细健康检查
 * GET /health/detailed
 */
router.get('/detailed',
  lenientRateLimit,
  validateHealthCheck,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const { provider } = req.query;
    const uptime = Date.now() - startTime;
    
    logger.info('详细健康检查请求', {
      requestId: req.requestId,
      provider,
    });

    try {
      // 获取 AI 服务状态
      const aiServicesStatus = await aiService.getHealthStatus();
      
      // 获取 SSE 连接状态
      const sseStats = {
        activeConnections: sseManager.getActiveConnectionCount(),
        status: 'up' as const,
      };

      // 获取系统信息
      const memUsage = process.memoryUsage();
      const systemInfo = {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        memory: {
          rss: `${(memUsage.rss / 1024 / 1024).toFixed(2)} MB`,
          heapTotal: `${(memUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
          heapUsed: `${(memUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
          external: `${(memUsage.external / 1024 / 1024).toFixed(2)} MB`,
        },
        uptime: `${Math.floor(uptime / 1000)} seconds`,
      };

      // 构建服务状态
      const services: Record<string, any> = {
        api: {
          status: 'up',
          latency: 0,
        },
        sse: sseStats,
        ...aiServicesStatus,
      };

      // 如果指定了提供商，只返回该提供商的状态
      if (provider && typeof provider === 'string') {
        const providerStatus = services[provider];
        if (!providerStatus) {
          return res.sendError(`提供商 ${provider} 不存在`, 404);
        }
        
        const healthStatus: HealthStatus = {
          status: providerStatus.status === 'up' ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
          uptime,
          version: '1.0.0',
          services: {
            [provider]: providerStatus,
          },
        };

        return res.sendSuccess(healthStatus, `${provider} 服务状态`);
      }

      // 确定整体健康状态
      const allServicesHealthy = Object.values(services).every(
        service => service.status === 'up'
      );

      const healthStatus: HealthStatus = {
        status: allServicesHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime,
        version: '1.0.0',
        services,
      };

      // 添加系统信息
      (healthStatus as any).system = systemInfo;
      (healthStatus as any).config = {
        nodeEnv: config.server.nodeEnv,
        logLevel: config.server.logLevel,
        providers: ['openai', 'gemini', 'vertex-ai'],
      };

      logger.info('详细健康检查完成', {
        requestId: req.requestId,
        status: healthStatus.status,
        servicesCount: Object.keys(services).length,
        uptime,
      });

      res.sendSuccess(healthStatus, '详细健康检查完成');
    } catch (error) {
      logger.error('详细健康检查失败', {
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });

      const healthStatus: HealthStatus = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime,
        version: '1.0.0',
        services: {
          api: {
            status: 'down',
            error: error instanceof Error ? error.message : '未知错误',
          },
        },
      };

      res.status(503).json({
        success: false,
        data: healthStatus,
        error: '服务健康检查失败',
        timestamp: new Date().toISOString(),
        requestId: req.requestId,
      });
    }
  })
);

/**
 * 存活检查（简单的 ping）
 * GET /health/ping
 */
router.get('/ping',
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - startTime,
    });
  })
);

/**
 * 就绪检查
 * GET /health/ready
 */
router.get('/ready',
  lenientRateLimit,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    try {
      // 检查关键服务是否就绪
      const aiServicesStatus = await aiService.getHealthStatus();
      
      // 检查是否有任何服务不可用
      const hasUnhealthyService = Object.values(aiServicesStatus).some(
        service => service.status !== 'up'
      );

      if (hasUnhealthyService) {
        return res.status(503).json({
          status: 'not ready',
          timestamp: new Date().toISOString(),
          services: aiServicesStatus,
        });
      }

      res.json({
        status: 'ready',
        timestamp: new Date().toISOString(),
        services: aiServicesStatus,
      });
    } catch (error) {
      logger.error('就绪检查失败', {
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });

      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : '未知错误',
      });
    }
  })
);

/**
 * 获取服务指标
 * GET /health/metrics
 */
router.get('/metrics',
  lenientRateLimit,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const uptime = Date.now() - startTime;
    const memUsage = process.memoryUsage();
    
    const metrics = {
      uptime: {
        seconds: Math.floor(uptime / 1000),
        human: `${Math.floor(uptime / 1000 / 60)} minutes`,
      },
      memory: {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external,
        heapUsedPercentage: ((memUsage.heapUsed / memUsage.heapTotal) * 100).toFixed(2),
      },
      sse: {
        activeConnections: sseManager.getActiveConnectionCount(),
      },
      process: {
        pid: process.pid,
        version: process.version,
        platform: process.platform,
        arch: process.arch,
      },
      timestamp: new Date().toISOString(),
    };

    res.sendSuccess(metrics, '服务指标获取成功');
  })
);

export default router;
