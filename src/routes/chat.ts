import { Router } from 'express';
import { 
  ExtendedRequest, 
  ExtendedResponse, 
  ChatCompletionRequest,
  ResponseMode 
} from '@/types';
import { aiService } from '@/services/ai-service';
import { SSEUtils } from '@/utils/sse';
import { asyncHandler } from '@/middleware/error-handler';
import { validateChatCompletion } from '@/middleware/validation';
import { smartAuth } from '@/middleware/auth';
import { aiApiRateLimit } from '@/middleware/rate-limiter';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * 聊天完成 API
 * POST /v1/chat/completions
 * 
 * 支持两种响应模式：
 * 1. JSON 模式：返回完整的响应对象
 * 2. SSE 流式模式：通过 Server-Sent Events 实时返回数据
 */
router.post('/completions', 
  aiApiRateLimit,
  smartAuth,
  validateChatCompletion,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const request: ChatCompletionRequest = req.body;
    
    logger.info('收到聊天完成请求', {
      requestId: req.requestId,
      model: request.model,
      provider: request.provider,
      stream: request.stream,
      messageCount: request.messages.length,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    // 确定响应模式
    const isStream = request.stream === true;
    const responseMode = isStream ? ResponseMode.STREAM : ResponseMode.JSON;

    if (responseMode === ResponseMode.STREAM) {
      // 流式响应模式
      
      // 检查客户端是否支持 SSE
      if (!SSEUtils.isSSESupported(req)) {
        return res.sendError('客户端不支持 Server-Sent Events', 400);
      }

      // 创建 SSE 连接
      const connection = SSEUtils.createConnection(req, res);

      try {
        // 生成流式响应
        await aiService.generateCompletionWithSmartRouting(
          request,
          ResponseMode.STREAM,
          connection
        );
      } catch (error) {
        // 错误已在服务层处理并通过 SSE 发送
        logger.error('流式聊天完成处理失败', {
          requestId: req.requestId,
          error: error instanceof Error ? error.message : '未知错误',
        });
      }
    } else {
      // JSON 响应模式
      try {
        const response = await aiService.generateCompletionWithSmartRouting(
          request,
          ResponseMode.JSON
        ) as any;

        logger.info('聊天完成请求处理成功', {
          requestId: req.requestId,
          model: response.model,
          usage: response.usage,
        });

        res.sendSuccess(response, '聊天完成生成成功');
      } catch (error) {
        logger.error('聊天完成请求处理失败', {
          requestId: req.requestId,
          error: error instanceof Error ? error.message : '未知错误',
        });
        throw error;
      }
    }
  })
);

/**
 * 批量聊天完成 API
 * POST /v1/chat/batch
 * 
 * 支持批量处理多个聊天完成请求
 */
router.post('/batch',
  aiApiRateLimit,
  smartAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const requests: ChatCompletionRequest[] = req.body.requests;
    
    if (!Array.isArray(requests) || requests.length === 0) {
      return res.sendError('请求数组不能为空', 400);
    }

    if (requests.length > 10) {
      return res.sendError('批量请求数量不能超过 10 个', 400);
    }

    logger.info('收到批量聊天完成请求', {
      requestId: req.requestId,
      requestCount: requests.length,
      user: req.user?.id || req.user?.sub || 'anonymous',
    });

    try {
      // 验证每个请求
      for (let i = 0; i < requests.length; i++) {
        const request = requests[i];
        if (!request.model || !request.messages) {
          return res.sendError(`请求 ${i} 缺少必需的字段`, 400);
        }
      }

      const responses = await aiService.generateBatchCompletions(requests);

      logger.info('批量聊天完成请求处理成功', {
        requestId: req.requestId,
        requestCount: requests.length,
        successCount: responses.length,
      });

      res.sendSuccess({
        responses,
        count: responses.length,
      }, '批量聊天完成生成成功');
    } catch (error) {
      logger.error('批量聊天完成请求处理失败', {
        requestId: req.requestId,
        requestCount: requests.length,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  })
);

/**
 * 获取聊天完成统计信息
 * GET /v1/chat/stats
 */
router.get('/stats',
  smartAuth,
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    const sseStats = SSEUtils.getStats();
    
    res.sendSuccess({
      sse: sseStats,
      timestamp: new Date().toISOString(),
    }, '统计信息获取成功');
  })
);

/**
 * 测试 SSE 连接
 * GET /v1/chat/test-stream
 */
router.get('/test-stream',
  asyncHandler(async (req: ExtendedRequest, res: ExtendedResponse) => {
    if (!SSEUtils.isSSESupported(req)) {
      return res.sendError('客户端不支持 Server-Sent Events', 400);
    }

    const connection = SSEUtils.createConnection(req, res);

    // 发送测试数据
    let count = 0;
    const interval = setInterval(() => {
      count++;
      
      const success = connection.send({
        type: 'test',
        count,
        message: `测试消息 ${count}`,
        timestamp: new Date().toISOString(),
      }, 'test');

      if (!success || count >= 5) {
        clearInterval(interval);
        if (connection.isActive()) {
          connection.sendComplete({ totalMessages: count });
        }
      }
    }, 1000);

    // 清理定时器
    res.on('close', () => {
      clearInterval(interval);
    });
  })
);

export default router;
