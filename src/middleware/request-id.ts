import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { ExtendedRequest } from '@/types';

/**
 * 请求 ID 中间件
 * 为每个请求生成唯一的 ID，用于日志追踪和调试
 */
export default function requestId(req: ExtendedRequest, res: Response, next: NextFunction): void {
  // 生成唯一的请求 ID
  req.requestId = req.headers['x-request-id'] as string || uuidv4();
  
  // 记录请求开始时间
  req.startTime = Date.now();
  
  // 在响应头中返回请求 ID
  res.setHeader('X-Request-ID', req.requestId);
  
  next();
}
