import { <PERSON><PERSON><PERSON>ider, ApiError, AuthMode, ExtendedRequest } from '@/types';
import { NextFunction, Response } from 'express';
import Jo<PERSON> from 'joi';

/**
 * 验证中间件工厂
 * 用于验证请求参数、查询参数和请求体
 */
export function validate(schema: {
  body?: Joi.ObjectSchema;
  query?: Joi.ObjectSchema;
  params?: Joi.ObjectSchema;
}) {
  return (req: ExtendedRequest, res: Response, next: NextFunction): void => {
    const errors: string[] = [];

    // 验证请求体
    if (schema.body) {
      const { error } = schema.body.validate(req.body);
      if (error) {
        errors.push(`请求体验证失败: ${error.details.map(d => d.message).join(', ')}`);
      }
    }

    // 验证查询参数
    if (schema.query) {
      const { error } = schema.query.validate(req.query);
      if (error) {
        errors.push(`查询参数验证失败: ${error.details.map(d => d.message).join(', ')}`);
      }
    }

    // 验证路径参数
    if (schema.params) {
      const { error } = schema.params.validate(req.params);
      if (error) {
        errors.push(`路径参数验证失败: ${error.details.map(d => d.message).join(', ')}`);
      }
    }

    if (errors.length > 0) {
      throw new ApiError(errors.join('; '), 400, 'VALIDATION_ERROR', { errors });
    }

    next();
  };
}

/**
 * 聊天完成请求验证模式
 */
export const chatCompletionSchema = Joi.object({
  model: Joi.string().required().messages({
    'string.empty': '模型名称不能为空',
    'any.required': '模型名称是必需的',
  }),

  messages: Joi.array()
    .items(
      Joi.object({
        role: Joi.string().valid('system', 'user', 'assistant').required().messages({
          'any.only': '消息角色必须是 system、user 或 assistant 之一',
          'any.required': '消息角色是必需的',
        }),
        content: Joi.string().required().messages({
          'string.empty': '消息内容不能为空',
          'any.required': '消息内容是必需的',
        }),
        name: Joi.string().optional(),
      })
    )
    .min(1)
    .required()
    .messages({
      'array.min': '至少需要一条消息',
      'any.required': '消息数组是必需的',
    }),

  temperature: Joi.number().min(0).max(2).optional().messages({
    'number.min': '温度参数不能小于 0',
    'number.max': '温度参数不能大于 2',
  }),

  max_tokens: Joi.number().integer().min(1).max(32000).optional().messages({
    'number.min': '最大令牌数必须大于 0',
    'number.max': '最大令牌数不能超过 32000',
    'number.integer': '最大令牌数必须是整数',
  }),

  top_p: Joi.number().min(0).max(1).optional().messages({
    'number.min': 'top_p 参数不能小于 0',
    'number.max': 'top_p 参数不能大于 1',
  }),

  frequency_penalty: Joi.number().min(-2).max(2).optional().messages({
    'number.min': '频率惩罚参数不能小于 -2',
    'number.max': '频率惩罚参数不能大于 2',
  }),

  presence_penalty: Joi.number().min(-2).max(2).optional().messages({
    'number.min': '存在惩罚参数不能小于 -2',
    'number.max': '存在惩罚参数不能大于 2',
  }),

  stop: Joi.alternatives()
    .try(
      Joi.string(),
      Joi.array().items(Joi.string()).max(4)
    )
    .optional()
    .messages({
      'array.max': '停止序列最多只能有 4 个',
    }),

  stream: Joi.boolean().optional(),

  provider: Joi.string()
    .valid(...Object.values(AIProvider))
    .optional()
    .messages({
      'any.only': `提供商必须是 ${Object.values(AIProvider).join('、')} 之一`,
    }),

  auth_mode: Joi.string()
    .valid(...Object.values(AuthMode))
    .optional()
    .messages({
      'any.only': `认证模式必须是 ${Object.values(AuthMode).join('、')} 之一`,
    }),
});

/**
 * 健康检查查询参数验证模式
 */
export const healthCheckQuerySchema = Joi.object({
  detailed: Joi.boolean().optional(),
  provider: Joi.string()
    .valid(...Object.values(AIProvider))
    .optional(),
});

/**
 * 分页查询参数验证模式
 */
export const paginationQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.min': '页码必须大于等于 1',
    'number.integer': '页码必须是整数',
  }),

  limit: Joi.number().integer().min(1).max(100).default(20).messages({
    'number.min': '每页数量必须大于等于 1',
    'number.max': '每页数量不能超过 100',
    'number.integer': '每页数量必须是整数',
  }),

  sort: Joi.string().optional(),
  order: Joi.string().valid('asc', 'desc').default('desc').optional(),
});

/**
 * 模型查询参数验证模式
 */
export const modelQuerySchema = Joi.object({
  provider: Joi.string()
    .valid(...Object.values(AIProvider))
    .optional(),

  search: Joi.string().optional(),

  ...paginationQuerySchema.describe().keys,
});

/**
 * 通用 ID 参数验证模式
 */
export const idParamsSchema = Joi.object({
  id: Joi.string().required().messages({
    'string.empty': 'ID 不能为空',
    'any.required': 'ID 是必需的',
  }),
});

/**
 * 提供商参数验证模式
 */
export const providerParamsSchema = Joi.object({
  provider: Joi.string()
    .valid(...Object.values(AIProvider))
    .required()
    .messages({
      'any.only': `提供商必须是 ${Object.values(AIProvider).join('、')} 之一`,
      'any.required': '提供商是必需的',
    }),
});

/**
 * 自定义验证函数
 */
export const customValidators = {
  /**
   * 验证消息数组的有效性
   */
  validateMessages: (messages: any[]): boolean => {
    if (!Array.isArray(messages) || messages.length === 0) {
      return false;
    }

    // 检查消息顺序的合理性
    let hasUser = false;
    for (const message of messages) {
      if (message.role === 'user') {
        hasUser = true;
      } else if (message.role === 'assistant' && !hasUser) {
        // 助手消息前必须有用户消息
        return false;
      }
    }

    return hasUser; // 至少要有一条用户消息
  },

  /**
   * 验证模型名称是否与提供商匹配
   */
  validateModelProvider: (model: string, provider?: AIProvider): boolean => {
    if (!provider) {
      return true; // 如果没有指定提供商，跳过验证
    }

    switch (provider) {
      case AIProvider.OPENAI:
        return model.startsWith('gpt-') || model.includes('davinci') || model.includes('curie');
      case AIProvider.GEMINI:
        return model.startsWith('gemini-') || model.includes('gemini');
      default:
        return true;
    }
  },

  /**
   * 验证停止序列的有效性
   */
  validateStopSequences: (stop: string | string[]): boolean => {
    if (typeof stop === 'string') {
      return stop.length > 0 && stop.length <= 10;
    }

    if (Array.isArray(stop)) {
      return stop.length <= 4 && stop.every(s => typeof s === 'string' && s.length > 0 && s.length <= 10);
    }

    return false;
  },
};

/**
 * 聊天完成请求验证中间件
 */
export const validateChatCompletion = validate({
  body: chatCompletionSchema.custom((value, helpers) => {
    // 自定义验证逻辑
    if (!customValidators.validateMessages(value.messages)) {
      return helpers.error('custom.invalidMessages');
    }

    if (!customValidators.validateModelProvider(value.model, value.provider)) {
      return helpers.error('custom.modelProviderMismatch');
    }

    if (value.stop && !customValidators.validateStopSequences(value.stop)) {
      return helpers.error('custom.invalidStopSequences');
    }

    return value;
  }, 'Chat completion validation').messages({
    'custom.invalidMessages': '消息数组格式无效或缺少用户消息',
    'custom.modelProviderMismatch': '模型名称与指定的提供商不匹配',
    'custom.invalidStopSequences': '停止序列格式无效',
  }),
});

/**
 * 健康检查验证中间件
 */
export const validateHealthCheck = validate({
  query: healthCheckQuerySchema,
});

/**
 * 模型列表验证中间件
 */
export const validateModelList = validate({
  query: modelQuerySchema,
});

/**
 * 提供商参数验证中间件
 */
export const validateProviderParams = validate({
  params: providerParamsSchema,
});

// 导出默认验证中间件
export default validate;
