import { Request, Response, NextFunction } from 'express';
import { ApiError, ExtendedRequest } from '@/types';
import { logger } from '@/utils/logger';
import { serverConfig } from '@/config';

/**
 * 全局错误处理中间件
 * 统一处理应用程序中的所有错误
 */
export default function errorHandler(
  error: Error | ApiError,
  req: ExtendedRequest,
  res: Response,
  next: NextFunction
): void {
  // 如果响应已经发送，则交给默认的 Express 错误处理器
  if (res.headersSent) {
    return next(error);
  }

  let statusCode = 500;
  let errorCode = 'INTERNAL_ERROR';
  let message = '服务器内部错误';
  let details: any = undefined;

  // 处理自定义 API 错误
  if (error instanceof ApiError) {
    statusCode = error.statusCode;
    errorCode = error.code;
    message = error.message;
    details = error.details;
  } else {
    // 处理其他类型的错误
    switch (error.name) {
      case 'ValidationError':
        statusCode = 400;
        errorCode = 'VALIDATION_ERROR';
        message = '请求参数验证失败';
        break;
      case 'UnauthorizedError':
        statusCode = 401;
        errorCode = 'UNAUTHORIZED';
        message = '未授权访问';
        break;
      case 'SyntaxError':
        statusCode = 400;
        errorCode = 'INVALID_JSON';
        message = '无效的 JSON 格式';
        break;
      case 'MongoError':
      case 'SequelizeError':
        statusCode = 500;
        errorCode = 'DATABASE_ERROR';
        message = '数据库操作失败';
        break;
      default:
        // 保持默认值
        break;
    }
  }

  // 记录错误日志
  logger.error('请求处理错误', {
    requestId: req.requestId,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      statusCode,
      errorCode,
    },
    request: {
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress,
    },
  });

  // 构建错误响应
  const errorResponse: any = {
    success: false,
    error: message,
    code: errorCode,
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  };

  // 在开发环境中包含更多错误信息
  if (serverConfig.nodeEnv === 'development') {
    errorResponse.stack = error.stack;
    errorResponse.details = details;
  }

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
}

/**
 * 404 错误处理中间件
 */
export function notFoundHandler(req: ExtendedRequest, res: Response): void {
  const message = `路由不存在: ${req.method} ${req.url}`;
  
  logger.warn('404 错误', {
    requestId: req.requestId,
    method: req.method,
    url: req.url,
    userAgent: req.headers['user-agent'],
    ip: req.ip || req.connection.remoteAddress,
  });

  res.status(404).json({
    success: false,
    error: message,
    code: 'NOT_FOUND',
    timestamp: new Date().toISOString(),
    requestId: req.requestId,
  });
}

/**
 * 异步错误包装器
 * 用于包装异步路由处理器，自动捕获 Promise 拒绝
 */
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
