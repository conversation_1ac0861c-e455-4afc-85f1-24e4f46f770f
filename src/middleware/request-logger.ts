import { Request, Response, NextFunction } from 'express';
import { ExtendedRequest } from '@/types';
import { RequestLogger } from '@/utils/logger';

/**
 * 请求日志中间件
 * 记录所有 HTTP 请求的详细信息
 */
export default function requestLogger(req: ExtendedRequest, res: Response, next: NextFunction): void {
  // 记录请求开始
  RequestLogger.logStart(req.requestId, req.method, req.url, {
    userAgent: req.headers['user-agent'],
    ip: req.ip || req.connection.remoteAddress,
    headers: {
      'content-type': req.headers['content-type'],
      'authorization': req.headers.authorization ? '[REDACTED]' : undefined,
      'x-api-key': req.headers['x-api-key'] ? '[REDACTED]' : undefined,
    },
    query: req.query,
    body: req.method === 'POST' || req.method === 'PUT' ? '[BODY_PRESENT]' : undefined,
  });

  // 监听响应结束事件
  res.on('finish', () => {
    const duration = Date.now() - req.startTime;
    
    RequestLogger.logEnd(req.requestId, res.statusCode, duration, {
      contentLength: res.getHeader('content-length'),
      contentType: res.getHeader('content-type'),
    });
  });

  // 监听响应错误事件
  res.on('error', (error: Error) => {
    RequestLogger.logError(req.requestId, error);
  });

  next();
}
