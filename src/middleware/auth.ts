import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { ExtendedRequest, ApiError, AuthMode } from '@/types';
import { authConfig } from '@/config';
import { logger } from '@/utils/logger';

/**
 * 认证中间件
 * 支持多种认证模式：API Key、Bearer Token、OAuth
 */

/**
 * API Key 认证中间件
 */
export function apiKeyAuth(req: ExtendedRequest, res: Response, next: NextFunction): void {
  const apiKey = req.headers[authConfig.apiKeyHeader.toLowerCase()] as string;
  
  if (!apiKey) {
    throw new ApiError('缺少 API Key', 401, 'MISSING_API_KEY');
  }

  // 这里应该验证 API Key 的有效性
  // 实际项目中应该从数据库或配置中验证
  if (!isValidApiKey(apiKey)) {
    logger.warn('无效的 API Key', {
      requestId: req.requestId,
      apiKey: apiKey.slice(0, 8) + '...',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    
    throw new ApiError('无效的 API Key', 401, 'INVALID_API_KEY');
  }

  // 将 API Key 信息添加到请求对象
  req.apiKey = apiKey;
  req.user = { apiKey, type: 'api_key' };
  
  next();
}

/**
 * Bearer Token 认证中间件
 */
export function bearerTokenAuth(req: ExtendedRequest, res: Response, next: NextFunction): void {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new ApiError('缺少 Bearer Token', 401, 'MISSING_BEARER_TOKEN');
  }

  const token = authHeader.slice(7); // 移除 'Bearer ' 前缀
  
  try {
    // 验证 JWT Token
    const decoded = jwt.verify(token, authConfig.jwtSecret) as any;
    
    req.user = decoded;
    
    logger.debug('Bearer Token 认证成功', {
      requestId: req.requestId,
      userId: decoded.sub || decoded.id,
      tokenType: decoded.type || 'jwt',
    });
    
    next();
  } catch (error) {
    logger.warn('无效的 Bearer Token', {
      requestId: req.requestId,
      token: token.slice(0, 10) + '...',
      error: error instanceof Error ? error.message : '未知错误',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    
    if (error instanceof jwt.TokenExpiredError) {
      throw new ApiError('Token 已过期', 401, 'TOKEN_EXPIRED');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new ApiError('无效的 Token', 401, 'INVALID_TOKEN');
    } else {
      throw new ApiError('Token 验证失败', 401, 'TOKEN_VERIFICATION_FAILED');
    }
  }
}

/**
 * 可选认证中间件
 * 如果提供了认证信息则验证，否则继续处理
 */
export function optionalAuth(req: ExtendedRequest, res: Response, next: NextFunction): void {
  const apiKey = req.headers[authConfig.apiKeyHeader.toLowerCase()] as string;
  const authHeader = req.headers.authorization;
  
  try {
    if (apiKey) {
      // 尝试 API Key 认证
      apiKeyAuth(req, res, next);
    } else if (authHeader && authHeader.startsWith('Bearer ')) {
      // 尝试 Bearer Token 认证
      bearerTokenAuth(req, res, next);
    } else {
      // 没有认证信息，继续处理
      next();
    }
  } catch (error) {
    // 认证失败，但不阻止请求继续处理
    logger.warn('可选认证失败', {
      requestId: req.requestId,
      error: error instanceof Error ? error.message : '未知错误',
    });
    next();
  }
}

/**
 * 智能认证中间件
 * 根据请求自动选择合适的认证方式
 */
export function smartAuth(req: ExtendedRequest, res: Response, next: NextFunction): void {
  const apiKey = req.headers[authConfig.apiKeyHeader.toLowerCase()] as string;
  const authHeader = req.headers.authorization;
  
  // 确定认证模式
  let authMode: AuthMode;
  
  if (apiKey) {
    authMode = AuthMode.API_KEY;
  } else if (authHeader && authHeader.startsWith('Bearer ')) {
    authMode = AuthMode.BEARER_TOKEN;
  } else {
    throw new ApiError('缺少认证信息', 401, 'MISSING_AUTHENTICATION');
  }

  // 将认证模式添加到请求体（如果是 AI API 请求）
  if (req.body && typeof req.body === 'object') {
    req.body.auth_mode = authMode;
  }

  // 执行相应的认证
  switch (authMode) {
    case AuthMode.API_KEY:
      apiKeyAuth(req, res, next);
      break;
    case AuthMode.BEARER_TOKEN:
      bearerTokenAuth(req, res, next);
      break;
    default:
      throw new ApiError('不支持的认证模式', 400, 'UNSUPPORTED_AUTH_MODE');
  }
}

/**
 * 权限检查中间件工厂
 */
export function requirePermission(permission: string) {
  return (req: ExtendedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new ApiError('未认证', 401, 'UNAUTHENTICATED');
    }

    // 检查用户权限
    if (!hasPermission(req.user, permission)) {
      logger.warn('权限不足', {
        requestId: req.requestId,
        userId: req.user.id || req.user.sub,
        requiredPermission: permission,
        userPermissions: req.user.permissions || [],
      });
      
      throw new ApiError('权限不足', 403, 'INSUFFICIENT_PERMISSIONS');
    }

    next();
  };
}

/**
 * 角色检查中间件工厂
 */
export function requireRole(role: string) {
  return (req: ExtendedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new ApiError('未认证', 401, 'UNAUTHENTICATED');
    }

    if (!hasRole(req.user, role)) {
      logger.warn('角色不匹配', {
        requestId: req.requestId,
        userId: req.user.id || req.user.sub,
        requiredRole: role,
        userRoles: req.user.roles || [],
      });
      
      throw new ApiError('角色权限不足', 403, 'INSUFFICIENT_ROLE');
    }

    next();
  };
}

/**
 * 验证 API Key 是否有效
 */
function isValidApiKey(apiKey: string): boolean {
  // 这里应该实现实际的 API Key 验证逻辑
  // 例如：从数据库查询、检查格式、验证签名等
  
  // 简单的示例验证（实际项目中应该更严格）
  return apiKey.length >= 32 && /^[a-zA-Z0-9_-]+$/.test(apiKey);
}

/**
 * 检查用户是否具有指定权限
 */
function hasPermission(user: any, permission: string): boolean {
  if (!user.permissions) {
    return false;
  }
  
  return user.permissions.includes(permission) || user.permissions.includes('*');
}

/**
 * 检查用户是否具有指定角色
 */
function hasRole(user: any, role: string): boolean {
  if (!user.roles) {
    return false;
  }
  
  return user.roles.includes(role) || user.roles.includes('admin');
}

// 导出默认的认证中间件
export default smartAuth;
