import { Request, Response, NextFunction } from 'express';
import { ExtendedRequest, ExtendedResponse, ApiResponse } from '@/types';

/**
 * 响应助手中间件
 * 为 Response 对象添加便捷的响应方法
 */
export default function responseHelpers(
  req: ExtendedRequest,
  res: ExtendedResponse,
  next: NextFunction
): void {
  /**
   * 发送成功响应
   */
  res.sendSuccess = function(data?: any, message?: string): void {
    const response: ApiResponse = {
      success: true,
      data,
      message: message || '操作成功',
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
    };

    this.status(200).json(response);
  };

  /**
   * 发送错误响应
   */
  res.sendError = function(error: string, statusCode: number = 500): void {
    const response: ApiResponse = {
      success: false,
      error,
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
    };

    this.status(statusCode).json(response);
  };

  next();
}
