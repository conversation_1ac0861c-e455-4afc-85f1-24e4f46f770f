import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { serverConfig } from '@/config';
import { ExtendedRequest } from '@/types';
import { logger } from '@/utils/logger';

/**
 * 速率限制配置
 */
const rateLimitConfig = {
  windowMs: serverConfig.rateLimitWindowMs, // 时间窗口
  max: serverConfig.rateLimitMaxRequests, // 最大请求数
  message: {
    success: false,
    error: '请求频率过高，请稍后重试',
    code: 'RATE_LIMITED',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true, // 返回标准的 `RateLimit` 头部
  legacyHeaders: false, // 禁用 `X-RateLimit-*` 头部
  
  // 自定义键生成器（基于 IP 和用户标识）
  keyGenerator: (req: ExtendedRequest): string => {
    // 优先使用 API 密钥，其次使用 IP 地址
    const apiKey = req.headers['x-api-key'] || req.headers.authorization;
    if (apiKey) {
      return `api_key:${Buffer.from(apiKey as string).toString('base64').slice(0, 10)}`;
    }
    return `ip:${req.ip || req.connection.remoteAddress || 'unknown'}`;
  },

  // 跳过某些请求的速率限制
  skip: (req: ExtendedRequest): boolean => {
    // 跳过健康检查端点
    if (req.path === '/health' || req.path === '/ping') {
      return true;
    }
    
    // 跳过静态资源
    if (req.path.startsWith('/static/') || req.path.startsWith('/assets/')) {
      return true;
    }
    
    return false;
  },

  // 自定义处理器
  handler: (req: ExtendedRequest, res: Response) => {
    const key = rateLimitConfig.keyGenerator(req);
    
    logger.warn('速率限制触发', {
      requestId: req.requestId,
      key,
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress,
    });

    res.status(429).json({
      ...rateLimitConfig.message,
      requestId: req.requestId,
      retryAfter: Math.ceil(serverConfig.rateLimitWindowMs / 1000),
    });
  },

  // 成功回调
  onLimitReached: (req: ExtendedRequest) => {
    const key = rateLimitConfig.keyGenerator(req);
    
    logger.warn('速率限制达到上限', {
      requestId: req.requestId,
      key,
      method: req.method,
      url: req.url,
      windowMs: serverConfig.rateLimitWindowMs,
      maxRequests: serverConfig.rateLimitMaxRequests,
    });
  },
};

/**
 * 通用速率限制中间件
 */
export const generalRateLimit = rateLimit(rateLimitConfig);

/**
 * 严格的速率限制（用于敏感操作）
 */
export const strictRateLimit = rateLimit({
  ...rateLimitConfig,
  windowMs: 60 * 1000, // 1分钟
  max: 10, // 最多10次请求
  message: {
    success: false,
    error: '敏感操作请求频率过高，请稍后重试',
    code: 'STRICT_RATE_LIMITED',
    timestamp: new Date().toISOString(),
  },
});

/**
 * 宽松的速率限制（用于公共端点）
 */
export const lenientRateLimit = rateLimit({
  ...rateLimitConfig,
  windowMs: 60 * 1000, // 1分钟
  max: 1000, // 最多1000次请求
  message: {
    success: false,
    error: '请求频率过高，请稍后重试',
    code: 'LENIENT_RATE_LIMITED',
    timestamp: new Date().toISOString(),
  },
});

/**
 * AI API 专用速率限制
 */
export const aiApiRateLimit = rateLimit({
  ...rateLimitConfig,
  windowMs: 60 * 1000, // 1分钟
  max: 60, // 最多60次请求（每秒1次）
  message: {
    success: false,
    error: 'AI API 请求频率过高，请稍后重试',
    code: 'AI_API_RATE_LIMITED',
    timestamp: new Date().toISOString(),
  },
  
  // AI API 特殊的键生成器
  keyGenerator: (req: ExtendedRequest): string => {
    const apiKey = req.headers['x-api-key'] || req.headers.authorization;
    const model = req.body?.model || 'unknown';
    const provider = req.body?.provider || 'unknown';
    
    if (apiKey) {
      return `ai_api:${Buffer.from(apiKey as string).toString('base64').slice(0, 10)}:${provider}:${model}`;
    }
    return `ai_api:ip:${req.ip || req.connection.remoteAddress || 'unknown'}:${provider}:${model}`;
  },
});

/**
 * 创建自定义速率限制器
 */
export function createCustomRateLimit(options: {
  windowMs: number;
  max: number;
  message?: string;
  keyGenerator?: (req: ExtendedRequest) => string;
}) {
  return rateLimit({
    ...rateLimitConfig,
    windowMs: options.windowMs,
    max: options.max,
    message: {
      success: false,
      error: options.message || '请求频率过高，请稍后重试',
      code: 'CUSTOM_RATE_LIMITED',
      timestamp: new Date().toISOString(),
    },
    ...(options.keyGenerator && { keyGenerator: options.keyGenerator }),
  });
}

// 导出默认的速率限制中间件
export default generalRateLimit;
