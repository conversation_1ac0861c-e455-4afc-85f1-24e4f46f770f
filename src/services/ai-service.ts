import { AdapterFactory, aiServiceManager } from '@/adapters';
import {
  AIProvider,
  ApiError,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ResponseMode
} from '@/types';
import { logger, PerformanceLogger } from '@/utils/logger';
import { SSEConnection } from '@/utils/sse';

/**
 * AI 服务类
 * 提供统一的 AI 服务接口，处理不同提供商的请求
 */
export class AIService {
  private static instance: AIService;

  private constructor() { }

  public static getInstance(): AIService {
    if (!this.instance) {
      this.instance = new AIService();
    }
    return this.instance;
  }

  /**
   * 生成聊天完成响应
   */
  public async generateCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const startTime = Date.now();
    const requestId = `completion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // 获取适配器（优先使用动态配置）
      const adapter = this.getAdapterForModel(request.model);

      // 记录性能开始
      PerformanceLogger.start(requestId);

      logger.info('开始生成聊天完成', {
        requestId,
        model: request.model,
        messageCount: request.messages.length,
        stream: false,
        isDynamic: AdapterFactory.isDynamicModel(request.model),
      });

      // 生成响应
      const response = await adapter.generateCompletion(request);

      // 记录性能结束
      const duration = PerformanceLogger.end(requestId, {
        model: request.model,
        usage: response.usage,
      });

      logger.info('聊天完成生成成功', {
        requestId,
        model: response.model,
        duration: `${duration}ms`,
        usage: response.usage,
      });

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('聊天完成生成失败', {
        requestId,
        model: request.model,
        duration: `${duration}ms`,
        error: error instanceof Error ? error.message : '未知错误',
      });

      throw error;
    }
  }

  /**
   * 生成流式聊天完成响应
   */
  public async generateCompletionStream(
    request: ChatCompletionRequest,
    connection: SSEConnection
  ): Promise<void> {
    const startTime = Date.now();
    const requestId = `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // 获取适配器（优先使用动态配置）
      const adapter = this.getAdapterForModel(request.model);

      logger.info('开始生成流式聊天完成', {
        requestId,
        model: request.model,
        messageCount: request.messages.length,
        stream: true,
        isDynamic: AdapterFactory.isDynamicModel(request.model),
      });

      // 发送开始事件
      connection.send({
        type: 'start',
        requestId,
        model: request.model,
      }, 'start');

      let chunkCount = 0;
      let totalTokens = 0;

      // 生成流式响应
      const stream = adapter.generateCompletionStream(request);

      for await (const chunk of stream) {
        // 检查连接是否仍然活跃
        if (!connection.isActive()) {
          logger.warn('SSE 连接已断开，停止流式生成', { requestId });
          break;
        }

        // 发送数据块
        const success = connection.sendChunk(chunk);
        if (!success) {
          logger.warn('发送 SSE 数据块失败，停止流式生成', { requestId });
          break;
        }

        chunkCount++;

        // 统计令牌使用（如果可用）
        if (chunk.choices[0]?.delta?.content) {
          totalTokens += chunk.choices[0].delta.content.length; // 简单估算
        }
      }

      const duration = Date.now() - startTime;

      // 发送完成事件
      connection.sendComplete({
        requestId,
        chunkCount,
        duration: `${duration}ms`,
        estimatedTokens: totalTokens,
      });

      logger.info('流式聊天完成生成成功', {
        requestId,
        model: request.model,
        duration: `${duration}ms`,
        chunkCount,
        estimatedTokens: totalTokens,
      });

    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('流式聊天完成生成失败', {
        requestId,
        provider: request.provider,
        model: request.model,
        duration: `${duration}ms`,
        error: error instanceof Error ? error.message : '未知错误',
      });

      // 发送错误事件
      if (connection.isActive()) {
        const errorMessage = error instanceof ApiError ? error.message : '生成过程中发生错误';
        const errorCode = error instanceof ApiError ? error.code : 'GENERATION_ERROR';
        connection.sendError(errorMessage, errorCode);
      }

      throw error;
    }
  }

  /**
   * 根据模型名称获取适配器
   */
  private getAdapterForModel(modelName: string): any {
    // 优先检查是否为动态配置的模型
    if (AdapterFactory.isDynamicModel(modelName)) {
      return AdapterFactory.getDynamicAdapter(modelName);
    }

    // 使用传统方式获取适配器
    const provider = AdapterFactory.detectProvider(modelName);
    return aiServiceManager.getAdapter(provider);
  }

  /**
   * 智能路由：根据请求选择最佳提供商
   */
  public async generateCompletionWithSmartRouting(
    request: ChatCompletionRequest,
    mode: ResponseMode = ResponseMode.JSON,
    connection?: SSEConnection
  ): Promise<ChatCompletionResponse | void> {
    if (mode === ResponseMode.STREAM && connection) {
      return await this.generateCompletionStream(request, connection);
    } else {
      return await this.generateCompletion(request);
    }
  }

  /**
   * 批量生成聊天完成
   */
  public async generateBatchCompletions(
    requests: ChatCompletionRequest[]
  ): Promise<ChatCompletionResponse[]> {
    const startTime = Date.now();
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    logger.info('开始批量生成聊天完成', {
      batchId,
      requestCount: requests.length,
    });

    try {
      // 并行处理所有请求
      const promises = requests.map((request, index) =>
        this.generateCompletion(request).catch(error => {
          logger.error(`批量请求 ${index} 失败:`, error);
          throw error;
        })
      );

      const responses = await Promise.all(promises);
      const duration = Date.now() - startTime;

      logger.info('批量聊天完成生成成功', {
        batchId,
        requestCount: requests.length,
        successCount: responses.length,
        duration: `${duration}ms`,
      });

      return responses;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('批量聊天完成生成失败', {
        batchId,
        requestCount: requests.length,
        duration: `${duration}ms`,
        error: error instanceof Error ? error.message : '未知错误',
      });

      throw error;
    }
  }

  /**
   * 获取支持的模型列表
   */
  public async getSupportedModels(provider?: AIProvider): Promise<any[]> {
    const models: any[] = [];

    // 首先添加动态配置的模型
    const dynamicModels = AdapterFactory.getDynamicModels();
    models.push(...dynamicModels.map(config => ({
      id: config.name,
      provider: config.provider,
      name: config.name,
      actualModel: config.modelName,
      baseUrl: config.baseUrl,
      supportsVision: config.supportsVision,
      description: config.description,
      isDynamic: true,
    })));

    if (provider) {
      // 获取指定提供商的传统模型
      try {
        const adapter = aiServiceManager.getAdapter(provider);
        if ('getSupportedModels' in adapter) {
          const providerModels = (adapter as any).getSupportedModels();
          models.push(...providerModels.map((model: string) => ({
            id: model,
            provider,
            name: model,
            isDynamic: false,
          })));
        }
      } catch (error) {
        logger.warn(`获取 ${provider} 模型列表失败:`, error);
      }
    } else {
      // 获取所有提供商的传统模型
      for (const providerValue of Object.values(AIProvider)) {
        try {
          const adapter = aiServiceManager.getAdapter(providerValue);
          if ('getSupportedModels' in adapter) {
            const providerModels = (adapter as any).getSupportedModels();
            models.push(...providerModels.map((model: string) => ({
              id: model,
              provider: providerValue,
              name: model,
              isDynamic: false,
            })));
          }
        } catch (error) {
          logger.warn(`获取 ${providerValue} 模型列表失败:`, error);
        }
      }
    }

    return models;
  }

  /**
   * 获取服务健康状态
   */
  public async getHealthStatus(): Promise<any> {
    return await aiServiceManager.getAllAdaptersStatus();
  }

  /**
   * 验证模型是否支持
   */
  public async validateModel(model: string, provider?: AIProvider): Promise<boolean> {
    try {
      // 首先检查是否为动态配置的模型
      if (AdapterFactory.isDynamicModel(model)) {
        return true;
      }

      if (provider) {
        const adapter = aiServiceManager.getAdapter(provider);
        return 'isModelSupported' in adapter ? (adapter as any).isModelSupported(model) : true;
      } else {
        // 检查所有提供商
        for (const providerValue of Object.values(AIProvider)) {
          try {
            const adapter = aiServiceManager.getAdapter(providerValue);
            if ('isModelSupported' in adapter && (adapter as any).isModelSupported(model)) {
              return true;
            }
          } catch (error) {
            // 忽略单个提供商的错误
          }
        }
        return false;
      }
    } catch (error) {
      logger.error('验证模型支持失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const aiService = AIService.getInstance();
